{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d1-bestz<PERSON><PERSON>/src/lib/utils/cn.js"], "sourcesContent": ["import { clsx } from \"clsx\";\nimport { twMerge } from \"tailwind-merge\";\n\n/**\n * Utility function to merge Tailwind CSS classes\n * @param {...any} inputs - Class names to merge\n * @returns {string} Merged class names\n */\nexport function cn(...inputs) {\n  return twMerge(clsx(inputs));\n}\n\n/**\n * Utility function to format currency\n * @param {number} amount - Amount to format\n * @param {string} currency - Currency code (default: USD)\n * @returns {string} Formatted currency string\n */\nexport function formatCurrency(amount, currency = 'USD') {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: currency,\n  }).format(amount);\n}\n\n/**\n * Utility function to format relative time\n * @param {Date} date - Date to format\n * @returns {string} Relative time string\n */\nexport function formatRelativeTime(date) {\n  const now = new Date();\n  const diffInSeconds = Math.floor((now - date) / 1000);\n  \n  if (diffInSeconds < 60) return 'just now';\n  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m ago`;\n  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h ago`;\n  return `${Math.floor(diffInSeconds / 86400)}d ago`;\n}\n\n/**\n * Utility function to generate random ID\n * @param {number} length - Length of ID (default: 8)\n * @returns {string} Random ID string\n */\nexport function generateId(length = 8) {\n  return Math.random().toString(36).substring(2, length + 2);\n}\n\n/**\n * Utility function to debounce function calls\n * @param {Function} func - Function to debounce\n * @param {number} wait - Wait time in milliseconds\n * @returns {Function} Debounced function\n */\nexport function debounce(func, wait) {\n  let timeout;\n  return function executedFunction(...args) {\n    const later = () => {\n      clearTimeout(timeout);\n      func(...args);\n    };\n    clearTimeout(timeout);\n    timeout = setTimeout(later, wait);\n  };\n}\n\n/**\n * Utility function to throttle function calls\n * @param {Function} func - Function to throttle\n * @param {number} limit - Limit in milliseconds\n * @returns {Function} Throttled function\n */\nexport function throttle(func, limit) {\n  let inThrottle;\n  return function executedFunction(...args) {\n    if (!inThrottle) {\n      func.apply(this, args);\n      inThrottle = true;\n      setTimeout(() => inThrottle = false, limit);\n    }\n  };\n}\n\n/**\n * Utility function to check if element is in viewport\n * @param {Element} element - DOM element to check\n * @returns {boolean} Whether element is in viewport\n */\nexport function isInViewport(element) {\n  const rect = element.getBoundingClientRect();\n  return (\n    rect.top >= 0 &&\n    rect.left >= 0 &&\n    rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&\n    rect.right <= (window.innerWidth || document.documentElement.clientWidth)\n  );\n}\n\n/**\n * Utility function to smooth scroll to element\n * @param {string} elementId - ID of element to scroll to\n * @param {number} offset - Offset from top (default: 0)\n */\nexport function scrollToElement(elementId, offset = 0) {\n  const element = document.getElementById(elementId);\n  if (element) {\n    const elementPosition = element.getBoundingClientRect().top;\n    const offsetPosition = elementPosition + window.pageYOffset - offset;\n    \n    window.scrollTo({\n      top: offsetPosition,\n      behavior: 'smooth'\n    });\n  }\n}\n\n/**\n * Utility function to get random item from array\n * @param {Array} array - Array to get random item from\n * @returns {any} Random item from array\n */\nexport function getRandomItem(array) {\n  return array[Math.floor(Math.random() * array.length)];\n}\n\n/**\n * Utility function to shuffle array\n * @param {Array} array - Array to shuffle\n * @returns {Array} Shuffled array\n */\nexport function shuffleArray(array) {\n  const shuffled = [...array];\n  for (let i = shuffled.length - 1; i > 0; i--) {\n    const j = Math.floor(Math.random() * (i + 1));\n    [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];\n  }\n  return shuffled;\n}\n\n/**\n * Utility function to validate email\n * @param {string} email - Email to validate\n * @returns {boolean} Whether email is valid\n */\nexport function isValidEmail(email) {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n  return emailRegex.test(email);\n}\n\n/**\n * Utility function to capitalize first letter\n * @param {string} string - String to capitalize\n * @returns {string} Capitalized string\n */\nexport function capitalize(string) {\n  return string.charAt(0).toUpperCase() + string.slice(1);\n}\n\n/**\n * Utility function to truncate text\n * @param {string} text - Text to truncate\n * @param {number} maxLength - Maximum length\n * @returns {string} Truncated text\n */\nexport function truncateText(text, maxLength) {\n  if (text.length <= maxLength) return text;\n  return text.substring(0, maxLength) + '...';\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;AACA;;;AAOO,SAAS,GAAG,GAAG,MAAM;IAC1B,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,wIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAQO,SAAS,eAAe,MAAM,EAAE,WAAW,KAAK;IACrD,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;IACZ,GAAG,MAAM,CAAC;AACZ;AAOO,SAAS,mBAAmB,IAAI;IACrC,MAAM,MAAM,IAAI;IAChB,MAAM,gBAAgB,KAAK,KAAK,CAAC,CAAC,MAAM,IAAI,IAAI;IAEhD,IAAI,gBAAgB,IAAI,OAAO;IAC/B,IAAI,gBAAgB,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC;IACzE,IAAI,gBAAgB,OAAO,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,MAAM,KAAK,CAAC;IAC5E,OAAO,GAAG,KAAK,KAAK,CAAC,gBAAgB,OAAO,KAAK,CAAC;AACpD;AAOO,SAAS,WAAW,SAAS,CAAC;IACnC,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,SAAS,CAAC,GAAG,SAAS;AAC1D;AAQO,SAAS,SAAS,IAAI,EAAE,IAAI;IACjC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,MAAM,QAAQ;YACZ,aAAa;YACb,QAAQ;QACV;QACA,aAAa;QACb,UAAU,WAAW,OAAO;IAC9B;AACF;AAQO,SAAS,SAAS,IAAI,EAAE,KAAK;IAClC,IAAI;IACJ,OAAO,SAAS,iBAAiB,GAAG,IAAI;QACtC,IAAI,CAAC,YAAY;YACf,KAAK,KAAK,CAAC,IAAI,EAAE;YACjB,aAAa;YACb,WAAW,IAAM,aAAa,OAAO;QACvC;IACF;AACF;AAOO,SAAS,aAAa,OAAO;IAClC,MAAM,OAAO,QAAQ,qBAAqB;IAC1C,OACE,KAAK,GAAG,IAAI,KACZ,KAAK,IAAI,IAAI,KACb,KAAK,MAAM,IAAI,CAAC,OAAO,WAAW,IAAI,SAAS,eAAe,CAAC,YAAY,KAC3E,KAAK,KAAK,IAAI,CAAC,OAAO,UAAU,IAAI,SAAS,eAAe,CAAC,WAAW;AAE5E;AAOO,SAAS,gBAAgB,SAAS,EAAE,SAAS,CAAC;IACnD,MAAM,UAAU,SAAS,cAAc,CAAC;IACxC,IAAI,SAAS;QACX,MAAM,kBAAkB,QAAQ,qBAAqB,GAAG,GAAG;QAC3D,MAAM,iBAAiB,kBAAkB,OAAO,WAAW,GAAG;QAE9D,OAAO,QAAQ,CAAC;YACd,KAAK;YACL,UAAU;QACZ;IACF;AACF;AAOO,SAAS,cAAc,KAAK;IACjC,OAAO,KAAK,CAAC,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,MAAM,MAAM,EAAE;AACxD;AAOO,SAAS,aAAa,KAAK;IAChC,MAAM,WAAW;WAAI;KAAM;IAC3B,IAAK,IAAI,IAAI,SAAS,MAAM,GAAG,GAAG,IAAI,GAAG,IAAK;QAC5C,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,KAAK,CAAC,IAAI,CAAC;QAC3C,CAAC,QAAQ,CAAC,EAAE,EAAE,QAAQ,CAAC,EAAE,CAAC,GAAG;YAAC,QAAQ,CAAC,EAAE;YAAE,QAAQ,CAAC,EAAE;SAAC;IACzD;IACA,OAAO;AACT;AAOO,SAAS,aAAa,KAAK;IAChC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAOO,SAAS,WAAW,MAAM;IAC/B,OAAO,OAAO,MAAM,CAAC,GAAG,WAAW,KAAK,OAAO,KAAK,CAAC;AACvD;AAQO,SAAS,aAAa,IAAI,EAAE,SAAS;IAC1C,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,SAAS,CAAC,GAAG,aAAa;AACxC", "debugId": null}}, {"offset": {"line": 118, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d1-bestz<PERSON><PERSON>/src/lib/data/sampleData.js"], "sourcesContent": ["// BestzDealAi - Sample Data for MVP Demo\n\nexport const heroData = {\n  title: \"BestzDealAi\",\n  subtitle: \"AI-Powered Reverse Marketplace\",\n  description: \"Post what you want, sellers compete to offer the best deal. One post — many offers.\",\n  tagline: \"You post it. They deal it.\",\n  cta: {\n    primary: \"Start Getting Deals\",\n    secondary: \"See How It Works\"\n  }\n};\n\nexport const problemSolutionData = {\n  problems: [\n    {\n      id: 1,\n      title: \"Time-Consuming Price Hunting\",\n      description: \"Spending hours comparing prices across multiple stores and websites\",\n      icon: \"⏰\",\n      stat: \"45 min average per purchase\"\n    },\n    {\n      id: 2,\n      title: \"Limited Local Discovery\",\n      description: \"Missing out on great deals from local sellers you never knew existed\",\n      icon: \"📍\",\n      stat: \"79% struggle to find local sellers\"\n    },\n    {\n      id: 3,\n      title: \"No Direct Negotiation\",\n      description: \"Unable to negotiate or get personalized offers from sellers\",\n      icon: \"💬\",\n      stat: \"82% want to negotiate prices\"\n    }\n  ],\n  solutions: [\n    {\n      id: 1,\n      title: \"Sellers Come to You\",\n      description: \"Post once and receive multiple competitive offers automatically\",\n      icon: \"🎯\",\n      benefit: \"Save 90% of search time\"\n    },\n    {\n      id: 2,\n      title: \"AI-Powered Matching\",\n      description: \"Smart algorithm finds the best value deals, not just lowest prices\",\n      icon: \"🤖\",\n      benefit: \"Get 30% better deals\"\n    },\n    {\n      id: 3,\n      title: \"Real-Time Competition\",\n      description: \"Watch sellers compete live with instant notifications and updates\",\n      icon: \"⚡\",\n      benefit: \"Prices drop in real-time\"\n    }\n  ]\n};\n\nexport const processSteps = [\n  {\n    id: 1,\n    title: \"Post Your Request\",\n    description: \"Tell us what you want, your budget, and preferences in 30 seconds\",\n    icon: \"📝\",\n    details: [\"Product description\", \"Budget range\", \"Location preference\", \"Timeline\"]\n  },\n  {\n    id: 2,\n    title: \"Sellers Compete\",\n    description: \"Local and online sellers submit their best offers with real photos\",\n    icon: \"🏆\",\n    details: [\"Multiple offers\", \"Real product photos\", \"Seller ratings\", \"Delivery options\"]\n  },\n  {\n    id: 3,\n    title: \"Choose & Save\",\n    description: \"Compare offers, chat with sellers, and pick the perfect deal\",\n    icon: \"💰\",\n    details: [\"Smart comparison\", \"Direct messaging\", \"Secure payment\", \"Deal completion\"]\n  }\n];\n\nexport const featuresData = [\n  {\n    id: 1,\n    title: \"Smart Deal Ranking\",\n    description: \"AI analyzes price, quality, seller rating, and delivery time to rank offers\",\n    icon: \"🧠\",\n    category: \"AI Technology\",\n    demo: \"ranking\"\n  },\n  {\n    id: 2,\n    title: \"Real-Time Offers\",\n    description: \"Watch offers come in live with instant notifications and price updates\",\n    icon: \"📱\",\n    category: \"Live Updates\",\n    demo: \"realtime\"\n  },\n  {\n    id: 3,\n    title: \"Local & Online Sellers\",\n    description: \"Connect with both neighborhood shops and online retailers in one place\",\n    icon: \"🌐\",\n    category: \"Marketplace\",\n    demo: \"sellers\"\n  },\n  {\n    id: 4,\n    title: \"Secure Messaging\",\n    description: \"Built-in chat system with offer context and file sharing capabilities\",\n    icon: \"💬\",\n    category: \"Communication\",\n    demo: \"chat\"\n  },\n  {\n    id: 5,\n    title: \"Deal Analytics\",\n    description: \"Track your savings, compare market prices, and optimize future requests\",\n    icon: \"📊\",\n    category: \"Analytics\",\n    demo: \"analytics\"\n  },\n  {\n    id: 6,\n    title: \"Mobile-First Design\",\n    description: \"Seamless experience across all devices with offline capability\",\n    icon: \"📲\",\n    category: \"Experience\",\n    demo: \"mobile\"\n  }\n];\n\nexport const competitorData = [\n  {\n    feature: \"Buyer-Initiated Requests\",\n    bestzdealai: true,\n    amazon: false,\n    ebay: false,\n    facebook: false\n  },\n  {\n    feature: \"AI Deal Ranking\",\n    bestzdealai: true,\n    amazon: false,\n    ebay: false,\n    facebook: false\n  },\n  {\n    feature: \"Real-Time Competition\",\n    bestzdealai: true,\n    amazon: false,\n    ebay: true,\n    facebook: false\n  },\n  {\n    feature: \"Local Seller Discovery\",\n    bestzdealai: true,\n    amazon: false,\n    ebay: false,\n    facebook: true\n  },\n  {\n    feature: \"Free for Buyers\",\n    bestzdealai: true,\n    amazon: true,\n    ebay: true,\n    facebook: true\n  },\n  {\n    feature: \"Integrated Chat\",\n    bestzdealai: true,\n    amazon: false,\n    ebay: true,\n    facebook: true\n  }\n];\n\nexport const testimonialsData = [\n  {\n    id: 1,\n    name: \"Sarah Chen\",\n    role: \"Small Business Owner\",\n    avatar: \"👩‍💼\",\n    rating: 5,\n    text: \"BestzDealAi helped me find customers I never would have reached. My sales increased 40% in the first month!\",\n    location: \"San Francisco, CA\"\n  },\n  {\n    id: 2,\n    name: \"Mike Rodriguez\",\n    role: \"Deal Hunter\",\n    avatar: \"👨‍💻\",\n    rating: 5,\n    text: \"I saved $800 on electronics last month. Sellers actually compete for my business - it's amazing!\",\n    location: \"Austin, TX\"\n  },\n  {\n    id: 3,\n    name: \"Emma Thompson\",\n    role: \"Busy Parent\",\n    avatar: \"👩‍👧\",\n    rating: 5,\n    text: \"No more endless price comparisons. I post what I need and get the best deals delivered to my inbox.\",\n    location: \"Seattle, WA\"\n  },\n  {\n    id: 4,\n    name: \"David Kim\",\n    role: \"Local Shop Owner\",\n    avatar: \"👨‍🔧\",\n    rating: 5,\n    text: \"Finally, a platform where I can compete with big retailers. My local expertise actually matters here.\",\n    location: \"Portland, OR\"\n  }\n];\n\nexport const pricingData = [\n  {\n    id: \"buyer\",\n    name: \"For Buyers\",\n    price: \"Free\",\n    period: \"Forever\",\n    description: \"Get the best deals without any fees\",\n    features: [\n      \"Unlimited deal requests\",\n      \"AI-powered offer ranking\",\n      \"Real-time notifications\",\n      \"Secure messaging\",\n      \"Deal analytics\",\n      \"Mobile app access\"\n    ],\n    cta: \"Start Getting Deals\",\n    popular: false,\n    color: \"primary\"\n  },\n  {\n    id: \"seller-basic\",\n    name: \"Seller Basic\",\n    price: \"$0\",\n    period: \"/month\",\n    description: \"Perfect for occasional sellers\",\n    features: [\n      \"5 offers per month\",\n      \"Basic seller profile\",\n      \"Standard support\",\n      \"Deal completion tracking\",\n      \"Basic analytics\"\n    ],\n    cta: \"Start Selling\",\n    popular: false,\n    color: \"surface\"\n  },\n  {\n    id: \"seller-pro\",\n    name: \"Seller Pro\",\n    price: \"$19.99\",\n    period: \"/month\",\n    description: \"For active sellers and small businesses\",\n    features: [\n      \"Unlimited offers\",\n      \"Priority placement\",\n      \"Advanced analytics\",\n      \"Custom seller profile\",\n      \"Priority support\",\n      \"Bulk offer tools\",\n      \"API access\"\n    ],\n    cta: \"Go Pro\",\n    popular: true,\n    color: \"accent\"\n  }\n];\n\nexport const trustData = [\n  {\n    id: 1,\n    title: \"Secure Payments\",\n    description: \"Bank-level encryption and secure payment processing\",\n    icon: \"🔒\"\n  },\n  {\n    id: 2,\n    title: \"Verified Sellers\",\n    description: \"All sellers go through identity and business verification\",\n    icon: \"✅\"\n  },\n  {\n    id: 3,\n    title: \"Money-Back Guarantee\",\n    description: \"100% satisfaction guarantee or your money back\",\n    icon: \"💯\"\n  },\n  {\n    id: 4,\n    title: \"24/7 Support\",\n    description: \"Round-the-clock customer support and dispute resolution\",\n    icon: \"🛟\"\n  }\n];\n\nexport const demoLevels = [\n  {\n    id: \"basic\",\n    name: \"Basic Demo\",\n    description: \"Simple product request with 2-3 offers\",\n    request: {\n      product: \"iPhone 15 Pro\",\n      budget: \"$800-900\",\n      location: \"San Francisco, CA\",\n      timeline: \"This week\"\n    },\n    offers: [\n      {\n        id: 1,\n        seller: \"TechHub SF\",\n        price: 849,\n        condition: \"New\",\n        rating: 4.8,\n        delivery: \"2 days\",\n        image: \"📱\"\n      },\n      {\n        id: 2,\n        seller: \"Mobile World\",\n        price: 829,\n        condition: \"New\",\n        rating: 4.6,\n        delivery: \"3 days\",\n        image: \"📱\"\n      }\n    ]\n  },\n  {\n    id: \"good\",\n    name: \"Enhanced Demo\",\n    description: \"Multiple offers with negotiation simulation\",\n    request: {\n      product: \"MacBook Pro 14-inch\",\n      budget: \"$1800-2200\",\n      location: \"Austin, TX\",\n      timeline: \"Next week\"\n    },\n    offers: [\n      {\n        id: 1,\n        seller: \"Austin Electronics\",\n        price: 1999,\n        condition: \"New\",\n        rating: 4.9,\n        delivery: \"1 day\",\n        image: \"💻\",\n        negotiable: true\n      },\n      {\n        id: 2,\n        seller: \"Tech Deals Pro\",\n        price: 1899,\n        condition: \"Open Box\",\n        rating: 4.7,\n        delivery: \"2 days\",\n        image: \"💻\",\n        negotiable: true\n      },\n      {\n        id: 3,\n        seller: \"Local Computer Store\",\n        price: 2050,\n        condition: \"New\",\n        rating: 4.8,\n        delivery: \"Same day\",\n        image: \"💻\",\n        negotiable: false\n      }\n    ]\n  },\n  {\n    id: \"best\",\n    name: \"Full Experience\",\n    description: \"Complete marketplace with chat, images, and ratings\",\n    request: {\n      product: \"Gaming Setup (PC + Monitor + Accessories)\",\n      budget: \"$2000-3000\",\n      location: \"Seattle, WA\",\n      timeline: \"Flexible\"\n    },\n    offers: [\n      {\n        id: 1,\n        seller: \"Gaming Paradise\",\n        price: 2499,\n        condition: \"New\",\n        rating: 4.9,\n        delivery: \"3 days\",\n        image: \"🖥️\",\n        negotiable: true,\n        chat: true,\n        verified: true\n      },\n      {\n        id: 2,\n        seller: \"PC Builder Pro\",\n        price: 2299,\n        condition: \"Custom Built\",\n        rating: 4.8,\n        delivery: \"5 days\",\n        image: \"🖥️\",\n        negotiable: true,\n        chat: true,\n        verified: true\n      },\n      {\n        id: 3,\n        seller: \"Tech Warehouse\",\n        price: 2799,\n        condition: \"New\",\n        rating: 4.6,\n        delivery: \"2 days\",\n        image: \"🖥️\",\n        negotiable: false,\n        chat: true,\n        verified: false\n      }\n    ]\n  }\n];\n"], "names": [], "mappings": "AAAA,yCAAyC;;;;;;;;;;;;AAElC,MAAM,WAAW;IACtB,OAAO;IACP,UAAU;IACV,aAAa;IACb,SAAS;IACT,KAAK;QACH,SAAS;QACT,WAAW;IACb;AACF;AAEO,MAAM,sBAAsB;IACjC,UAAU;QACR;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;QACR;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,MAAM;QACR;KACD;IACD,WAAW;QACT;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,SAAS;QACX;QACA;YACE,IAAI;Y<PERSON><PERSON>,OAAO;YACP,aAAa;YACb,MAAM;YACN,SAAS;QACX;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM;YACN,SAAS;QACX;KACD;AACH;AAEO,MAAM,eAAe;IAC1B;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;YAAC;YAAuB;YAAgB;YAAuB;SAAW;IACrF;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;YAAC;YAAmB;YAAuB;YAAkB;SAAmB;IAC3F;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,SAAS;YAAC;YAAoB;YAAoB;YAAkB;SAAkB;IACxF;CACD;AAEM,MAAM,eAAe;IAC1B;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;QACN,UAAU;QACV,MAAM;IACR;CACD;AAEM,MAAM,iBAAiB;IAC5B;QACE,SAAS;QACT,aAAa;QACb,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA;QACE,SAAS;QACT,aAAa;QACb,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA;QACE,SAAS;QACT,aAAa;QACb,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA;QACE,SAAS;QACT,aAAa;QACb,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA;QACE,SAAS;QACT,aAAa;QACb,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA;QACE,SAAS;QACT,aAAa;QACb,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;CACD;AAEM,MAAM,mBAAmB;IAC9B;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,QAAQ;QACR,QAAQ;QACR,MAAM;QACN,UAAU;IACZ;CACD;AAEM,MAAM,cAAc;IACzB;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,SAAS;QACT,OAAO;IACT;IACA;QACE,IAAI;QACJ,MAAM;QACN,OAAO;QACP,QAAQ;QACR,aAAa;QACb,UAAU;YACR;YACA;YACA;YACA;YACA;YACA;YACA;SACD;QACD,KAAK;QACL,SAAS;QACT,OAAO;IACT;CACD;AAEM,MAAM,YAAY;IACvB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;QACb,MAAM;IACR;CACD;AAEM,MAAM,aAAa;IACxB;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,SAAS;YACP,SAAS;YACT,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,QAAQ;gBACR,OAAO;gBACP,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,OAAO;YACT;YACA;gBACE,IAAI;gBACJ,QAAQ;gBACR,OAAO;gBACP,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,OAAO;YACT;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,SAAS;YACP,SAAS;YACT,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,QAAQ;gBACR,OAAO;gBACP,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,OAAO;gBACP,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,QAAQ;gBACR,OAAO;gBACP,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,OAAO;gBACP,YAAY;YACd;YACA;gBACE,IAAI;gBACJ,QAAQ;gBACR,OAAO;gBACP,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,OAAO;gBACP,YAAY;YACd;SACD;IACH;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,SAAS;YACP,SAAS;YACT,QAAQ;YACR,UAAU;YACV,UAAU;QACZ;QACA,QAAQ;YACN;gBACE,IAAI;gBACJ,QAAQ;gBACR,OAAO;gBACP,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,OAAO;gBACP,YAAY;gBACZ,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,QAAQ;gBACR,OAAO;gBACP,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,OAAO;gBACP,YAAY;gBACZ,MAAM;gBACN,UAAU;YACZ;YACA;gBACE,IAAI;gBACJ,QAAQ;gBACR,OAAO;gBACP,WAAW;gBACX,QAAQ;gBACR,UAAU;gBACV,OAAO;gBACP,YAAY;gBACZ,MAAM;gBACN,UAAU;YACZ;SACD;IACH;CACD", "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d1-bestzdealai/src/components/sections/HeroSection.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect } from 'react';\nimport { cn } from '@/lib/utils/cn';\nimport { heroData } from '@/lib/data/sampleData';\n\nexport default function HeroSection() {\n  const [typedText, setTypedText] = useState('');\n  const [currentIndex, setCurrentIndex] = useState(0);\n  const [isDeleting, setIsDeleting] = useState(false);\n  \n  const phrases = [\n    \"You post it. They deal it.\",\n    \"The deal comes to you.\",\n    \"Smart shopping starts here.\",\n    \"Why search when sellers find you?\"\n  ];\n\n  useEffect(() => {\n    const currentPhrase = phrases[currentIndex];\n    const timeout = setTimeout(() => {\n      if (!isDeleting) {\n        if (typedText.length < currentPhrase.length) {\n          setTypedText(currentPhrase.slice(0, typedText.length + 1));\n        } else {\n          setTimeout(() => setIsDeleting(true), 2000);\n        }\n      } else {\n        if (typedText.length > 0) {\n          setTypedText(typedText.slice(0, -1));\n        } else {\n          setIsDeleting(false);\n          setCurrentIndex((prev) => (prev + 1) % phrases.length);\n        }\n      }\n    }, isDeleting ? 50 : 100);\n\n    return () => clearTimeout(timeout);\n  }, [typedText, currentIndex, isDeleting, phrases]);\n\n  return (\n    <section className=\"relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-surface to-surface-light\">\n      {/* Animated Background Elements */}\n      <div className=\"absolute inset-0 overflow-hidden\">\n        {/* Floating Geometric Shapes */}\n        <div className=\"absolute top-20 left-10 w-20 h-20 bg-primary/20 rounded-full animate-float\" \n             style={{ animationDelay: '0s' }} />\n        <div className=\"absolute top-40 right-20 w-16 h-16 bg-secondary/20 rounded-lg rotate-45 animate-float\" \n             style={{ animationDelay: '1s' }} />\n        <div className=\"absolute bottom-40 left-20 w-24 h-24 bg-accent/20 rounded-full animate-float\" \n             style={{ animationDelay: '2s' }} />\n        <div className=\"absolute bottom-20 right-10 w-18 h-18 bg-primary/20 rounded-lg rotate-12 animate-float\" \n             style={{ animationDelay: '3s' }} />\n        \n        {/* Matrix Rain Effect */}\n        <div className=\"absolute inset-0 opacity-10\">\n          {Array.from({ length: 20 }).map((_, i) => (\n            <div\n              key={i}\n              className=\"absolute text-primary font-mono text-sm animate-matrix\"\n              style={{\n                left: `${Math.random() * 100}%`,\n                animationDelay: `${Math.random() * 20}s`,\n                animationDuration: `${15 + Math.random() * 10}s`\n              }}\n            >\n              {Array.from({ length: 10 }).map((_, j) => (\n                <div key={j} className=\"mb-2\">\n                  {Math.random() > 0.5 ? '1' : '0'}\n                </div>\n              ))}\n            </div>\n          ))}\n        </div>\n      </div>\n\n      {/* Main Content */}\n      <div className=\"relative z-10 max-w-6xl mx-auto px-6 text-center\">\n        {/* Logo and Brand */}\n        <div className=\"mb-8\">\n          <div className=\"inline-flex items-center gap-4 mb-4\">\n            {/* Custom SVG Logo */}\n            <div className=\"w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-xl flex items-center justify-center glow-primary\">\n              <svg viewBox=\"0 0 24 24\" className=\"w-10 h-10 text-white\" fill=\"currentColor\">\n                <path d=\"M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5\"/>\n              </svg>\n            </div>\n            <h1 className=\"text-5xl md:text-7xl font-bold gradient-text\">\n              {heroData.title}\n            </h1>\n          </div>\n          <p className=\"text-xl md:text-2xl text-foreground/80 font-medium\">\n            {heroData.subtitle}\n          </p>\n        </div>\n\n        {/* Typing Animation */}\n        <div className=\"mb-12\">\n          <div className=\"text-2xl md:text-4xl font-bold text-primary min-h-[3rem] flex items-center justify-center\">\n            <span className=\"border-r-2 border-primary animate-pulse pr-1\">\n              {typedText}\n            </span>\n          </div>\n        </div>\n\n        {/* Description */}\n        <p className=\"text-lg md:text-xl text-foreground/70 max-w-3xl mx-auto mb-12 leading-relaxed\">\n          {heroData.description}\n        </p>\n\n        {/* CTA Buttons */}\n        <div className=\"flex flex-col sm:flex-row gap-6 justify-center items-center mb-16\">\n          <button className=\"group relative px-8 py-4 bg-gradient-to-r from-primary to-accent text-white font-semibold rounded-xl text-lg transition-all duration-300 hover:scale-105 glow-primary hover:shadow-2xl\">\n            <span className=\"relative z-10\">{heroData.cta.primary}</span>\n            <div className=\"absolute inset-0 bg-gradient-to-r from-primary-dark to-accent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300\" />\n          </button>\n          \n          <button className=\"group px-8 py-4 border-2 border-primary text-primary font-semibold rounded-xl text-lg transition-all duration-300 hover:bg-primary hover:text-background hover:scale-105\">\n            {heroData.cta.secondary}\n          </button>\n        </div>\n\n        {/* Mini Demo Loop */}\n        <div className=\"relative max-w-4xl mx-auto\">\n          <div className=\"bg-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-primary/20 glow-primary\">\n            <h3 className=\"text-xl font-semibold mb-6 text-primary\">See It In Action</h3>\n            \n            {/* Simulated Demo Interface */}\n            <div className=\"grid md:grid-cols-2 gap-6\">\n              {/* Buyer Side */}\n              <div className=\"space-y-4\">\n                <h4 className=\"font-medium text-foreground/80\">1. You Post</h4>\n                <div className=\"bg-background/50 rounded-lg p-4 text-left\">\n                  <div className=\"text-sm text-foreground/60 mb-2\">Looking for:</div>\n                  <div className=\"font-medium\">iPhone 15 Pro</div>\n                  <div className=\"text-sm text-foreground/60 mt-2\">Budget: $800-900</div>\n                  <div className=\"text-sm text-foreground/60\">Location: San Francisco</div>\n                </div>\n              </div>\n\n              {/* Seller Side */}\n              <div className=\"space-y-4\">\n                <h4 className=\"font-medium text-foreground/80\">2. Sellers Compete</h4>\n                <div className=\"space-y-2\">\n                  {[\n                    { name: \"TechHub SF\", price: \"$849\", rating: \"4.8★\" },\n                    { name: \"Mobile World\", price: \"$829\", rating: \"4.6★\" },\n                    { name: \"Best Buy\", price: \"$899\", rating: \"4.7★\" }\n                  ].map((offer, index) => (\n                    <div \n                      key={index}\n                      className=\"bg-background/50 rounded-lg p-3 flex justify-between items-center text-sm animate-pulse-glow\"\n                      style={{ animationDelay: `${index * 0.5}s` }}\n                    >\n                      <span>{offer.name}</span>\n                      <div className=\"text-right\">\n                        <div className=\"font-bold text-success\">{offer.price}</div>\n                        <div className=\"text-xs text-foreground/60\">{offer.rating}</div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            </div>\n\n            {/* Result */}\n            <div className=\"mt-6 p-4 bg-success/20 rounded-lg border border-success/30\">\n              <div className=\"text-success font-semibold\">✅ Best Deal Found!</div>\n              <div className=\"text-sm text-foreground/70 mt-1\">\n                Saved $70 compared to retail price\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* Scroll Indicator */}\n        <div className=\"absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce\">\n          <div className=\"w-6 h-10 border-2 border-primary rounded-full flex justify-center\">\n            <div className=\"w-1 h-3 bg-primary rounded-full mt-2 animate-pulse\" />\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAE7C,MAAM,UAAU;QACd;QACA;QACA;QACA;KACD;IAED,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;iCAAE;YACR,MAAM,gBAAgB,OAAO,CAAC,aAAa;YAC3C,MAAM,UAAU;iDAAW;oBACzB,IAAI,CAAC,YAAY;wBACf,IAAI,UAAU,MAAM,GAAG,cAAc,MAAM,EAAE;4BAC3C,aAAa,cAAc,KAAK,CAAC,GAAG,UAAU,MAAM,GAAG;wBACzD,OAAO;4BACL;iEAAW,IAAM,cAAc;gEAAO;wBACxC;oBACF,OAAO;wBACL,IAAI,UAAU,MAAM,GAAG,GAAG;4BACxB,aAAa,UAAU,KAAK,CAAC,GAAG,CAAC;wBACnC,OAAO;4BACL,cAAc;4BACd;iEAAgB,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,QAAQ,MAAM;;wBACvD;oBACF;gBACF;gDAAG,aAAa,KAAK;YAErB;yCAAO,IAAM,aAAa;;QAC5B;gCAAG;QAAC;QAAW;QAAc;QAAY;KAAQ;IAEjD,qBACE,6LAAC;QAAQ,WAAU;;0BAEjB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;wBACV,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCACnC,6LAAC;wBAAI,WAAU;wBACV,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCACnC,6LAAC;wBAAI,WAAU;wBACV,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCACnC,6LAAC;wBAAI,WAAU;wBACV,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;kCAGnC,6LAAC;wBAAI,WAAU;kCACZ,MAAM,IAAI,CAAC;4BAAE,QAAQ;wBAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,6LAAC;gCAEC,WAAU;gCACV,OAAO;oCACL,MAAM,GAAG,KAAK,MAAM,KAAK,IAAI,CAAC,CAAC;oCAC/B,gBAAgB,GAAG,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;oCACxC,mBAAmB,GAAG,KAAK,KAAK,MAAM,KAAK,GAAG,CAAC,CAAC;gCAClD;0CAEC,MAAM,IAAI,CAAC;oCAAE,QAAQ;gCAAG,GAAG,GAAG,CAAC,CAAC,GAAG,kBAClC,6LAAC;wCAAY,WAAU;kDACpB,KAAK,MAAM,KAAK,MAAM,MAAM;uCADrB;;;;;+BATP;;;;;;;;;;;;;;;;0BAmBb,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,SAAQ;4CAAY,WAAU;4CAAuB,MAAK;sDAC7D,cAAA,6LAAC;gDAAK,GAAE;;;;;;;;;;;;;;;;kDAGZ,6LAAC;wCAAG,WAAU;kDACX,mIAAA,CAAA,WAAQ,CAAC,KAAK;;;;;;;;;;;;0CAGnB,6LAAC;gCAAE,WAAU;0CACV,mIAAA,CAAA,WAAQ,CAAC,QAAQ;;;;;;;;;;;;kCAKtB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAK,WAAU;0CACb;;;;;;;;;;;;;;;;kCAMP,6LAAC;wBAAE,WAAU;kCACV,mIAAA,CAAA,WAAQ,CAAC,WAAW;;;;;;kCAIvB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAO,WAAU;;kDAChB,6LAAC;wCAAK,WAAU;kDAAiB,mIAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,OAAO;;;;;;kDACrD,6LAAC;wCAAI,WAAU;;;;;;;;;;;;0CAGjB,6LAAC;gCAAO,WAAU;0CACf,mIAAA,CAAA,WAAQ,CAAC,GAAG,CAAC,SAAS;;;;;;;;;;;;kCAK3B,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAG,WAAU;8CAA0C;;;;;;8CAGxD,6LAAC;oCAAI,WAAU;;sDAEb,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAkC;;;;;;sEACjD,6LAAC;4DAAI,WAAU;sEAAc;;;;;;sEAC7B,6LAAC;4DAAI,WAAU;sEAAkC;;;;;;sEACjD,6LAAC;4DAAI,WAAU;sEAA6B;;;;;;;;;;;;;;;;;;sDAKhD,6LAAC;4CAAI,WAAU;;8DACb,6LAAC;oDAAG,WAAU;8DAAiC;;;;;;8DAC/C,6LAAC;oDAAI,WAAU;8DACZ;wDACC;4DAAE,MAAM;4DAAc,OAAO;4DAAQ,QAAQ;wDAAO;wDACpD;4DAAE,MAAM;4DAAgB,OAAO;4DAAQ,QAAQ;wDAAO;wDACtD;4DAAE,MAAM;4DAAY,OAAO;4DAAQ,QAAQ;wDAAO;qDACnD,CAAC,GAAG,CAAC,CAAC,OAAO,sBACZ,6LAAC;4DAEC,WAAU;4DACV,OAAO;gEAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;4DAAC;;8EAE3C,6LAAC;8EAAM,MAAM,IAAI;;;;;;8EACjB,6LAAC;oEAAI,WAAU;;sFACb,6LAAC;4EAAI,WAAU;sFAA0B,MAAM,KAAK;;;;;;sFACpD,6LAAC;4EAAI,WAAU;sFAA8B,MAAM,MAAM;;;;;;;;;;;;;2DAPtD;;;;;;;;;;;;;;;;;;;;;;8CAgBf,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CAAI,WAAU;sDAA6B;;;;;;sDAC5C,6LAAC;4CAAI,WAAU;sDAAkC;;;;;;;;;;;;;;;;;;;;;;;kCAQvD,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;sCACb,cAAA,6LAAC;gCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B;GAlLwB;KAAA", "debugId": null}}, {"offset": {"line": 1094, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d1-bestzdealai/src/components/sections/ProblemSolutionSection.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { cn } from '@/lib/utils/cn';\nimport { problemSolutionData } from '@/lib/data/sampleData';\n\nexport default function ProblemSolutionSection() {\n  const [isVisible, setIsVisible] = useState(false);\n  const sectionRef = useRef(null);\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n        }\n      },\n      { threshold: 0.2 }\n    );\n\n    if (sectionRef.current) {\n      observer.observe(sectionRef.current);\n    }\n\n    return () => observer.disconnect();\n  }, []);\n\n  return (\n    <section \n      ref={sectionRef}\n      className=\"py-20 bg-gradient-to-b from-surface-light to-background relative overflow-hidden\"\n    >\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 opacity-5\">\n        <div className=\"absolute top-10 left-10 w-32 h-32 bg-secondary rounded-full blur-3xl animate-pulse\" />\n        <div className=\"absolute bottom-10 right-10 w-40 h-40 bg-accent rounded-full blur-3xl animate-pulse\" style={{ animationDelay: '1s' }} />\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-6\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className={cn(\n            \"text-4xl md:text-5xl font-bold gradient-text mb-6 transition-all duration-1000\",\n            isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"\n          )}>\n            The Problem We Solve\n          </h2>\n          <p className={cn(\n            \"text-xl text-foreground/70 max-w-3xl mx-auto transition-all duration-1000 delay-200\",\n            isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"\n          )}>\n            Traditional shopping is broken. We're fixing it with AI-powered reverse marketplace technology.\n          </p>\n        </div>\n\n        {/* Problems Section */}\n        <div className=\"mb-20\">\n          <h3 className={cn(\n            \"text-2xl md:text-3xl font-bold text-center mb-12 text-error transition-all duration-1000 delay-300\",\n            isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"\n          )}>\n            😤 Current Shopping Problems\n          </h3>\n          \n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {problemSolutionData.problems.map((problem, index) => (\n              <div\n                key={problem.id}\n                className={cn(\n                  \"group bg-surface/50 backdrop-blur-sm rounded-2xl p-6 border border-error/20 hover:border-error/40 transition-all duration-500 hover:scale-105 hover:glow-secondary\",\n                  isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"\n                )}\n                style={{ transitionDelay: `${400 + index * 200}ms` }}\n              >\n                {/* Problem Icon */}\n                <div className=\"text-4xl mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  {problem.icon}\n                </div>\n                \n                {/* Problem Title */}\n                <h4 className=\"text-xl font-semibold mb-3 text-foreground group-hover:text-error transition-colors duration-300\">\n                  {problem.title}\n                </h4>\n                \n                {/* Problem Description */}\n                <p className=\"text-foreground/70 mb-4 leading-relaxed\">\n                  {problem.description}\n                </p>\n                \n                {/* Problem Stat */}\n                <div className=\"bg-error/10 rounded-lg p-3 border border-error/20\">\n                  <div className=\"text-error font-semibold text-sm\">\n                    📊 {problem.stat}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Transition Arrow */}\n        <div className=\"text-center mb-20\">\n          <div className={cn(\n            \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary to-accent rounded-full glow-primary transition-all duration-1000 delay-1000\",\n            isVisible ? \"opacity-100 scale-100 rotate-0\" : \"opacity-0 scale-50 rotate-180\"\n          )}>\n            <svg className=\"w-8 h-8 text-white\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M19 14l-7 7m0 0l-7-7m7 7V3\" />\n            </svg>\n          </div>\n        </div>\n\n        {/* Solutions Section */}\n        <div>\n          <h3 className={cn(\n            \"text-2xl md:text-3xl font-bold text-center mb-12 text-success transition-all duration-1000 delay-1200\",\n            isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"\n          )}>\n            ✨ Our Smart Solutions\n          </h3>\n          \n          <div className=\"grid md:grid-cols-3 gap-8\">\n            {problemSolutionData.solutions.map((solution, index) => (\n              <div\n                key={solution.id}\n                className={cn(\n                  \"group bg-surface/50 backdrop-blur-sm rounded-2xl p-6 border border-success/20 hover:border-success/40 transition-all duration-500 hover:scale-105 hover:glow-primary\",\n                  isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"\n                )}\n                style={{ transitionDelay: `${1300 + index * 200}ms` }}\n              >\n                {/* Solution Icon */}\n                <div className=\"text-4xl mb-4 group-hover:scale-110 transition-transform duration-300\">\n                  {solution.icon}\n                </div>\n                \n                {/* Solution Title */}\n                <h4 className=\"text-xl font-semibold mb-3 text-foreground group-hover:text-success transition-colors duration-300\">\n                  {solution.title}\n                </h4>\n                \n                {/* Solution Description */}\n                <p className=\"text-foreground/70 mb-4 leading-relaxed\">\n                  {solution.description}\n                </p>\n                \n                {/* Solution Benefit */}\n                <div className=\"bg-success/10 rounded-lg p-3 border border-success/20\">\n                  <div className=\"text-success font-semibold text-sm\">\n                    🎯 {solution.benefit}\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* Comparison Chart */}\n        <div className={cn(\n          \"mt-20 bg-gradient-to-r from-surface/50 to-surface-light/50 backdrop-blur-sm rounded-2xl p-8 border border-primary/20 transition-all duration-1000 delay-1900\",\n          isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"\n        )}>\n          <h4 className=\"text-2xl font-bold text-center mb-8 gradient-text\">\n            Traditional vs BestzDealAi\n          </h4>\n          \n          <div className=\"grid md:grid-cols-2 gap-8\">\n            {/* Traditional Way */}\n            <div className=\"space-y-4\">\n              <h5 className=\"text-lg font-semibold text-error mb-4\">❌ Traditional Shopping</h5>\n              <div className=\"space-y-3\">\n                {[\n                  \"Search multiple websites manually\",\n                  \"Compare prices one by one\",\n                  \"No negotiation power\",\n                  \"Miss local deals\",\n                  \"Time-consuming process\"\n                ].map((item, index) => (\n                  <div key={index} className=\"flex items-center gap-3 text-foreground/70\">\n                    <div className=\"w-2 h-2 bg-error rounded-full\" />\n                    <span>{item}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n\n            {/* BestzDealAi Way */}\n            <div className=\"space-y-4\">\n              <h5 className=\"text-lg font-semibold text-success mb-4\">✅ BestzDealAi Way</h5>\n              <div className=\"space-y-3\">\n                {[\n                  \"Post once, get multiple offers\",\n                  \"AI ranks best value deals\",\n                  \"Sellers compete for your business\",\n                  \"Discover local hidden gems\",\n                  \"Save time and money\"\n                ].map((item, index) => (\n                  <div key={index} className=\"flex items-center gap-3 text-foreground/70\">\n                    <div className=\"w-2 h-2 bg-success rounded-full\" />\n                    <span>{item}</span>\n                  </div>\n                ))}\n              </div>\n            </div>\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;4CAAE;YACR,MAAM,WAAW,IAAI;oDACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;oBACf;gBACF;mDACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,WAAW,OAAO,EAAE;gBACtB,SAAS,OAAO,CAAC,WAAW,OAAO;YACrC;YAEA;oDAAO,IAAM,SAAS,UAAU;;QAClC;2CAAG,EAAE;IAEL,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;wBAAsF,OAAO;4BAAE,gBAAgB;wBAAK;;;;;;;;;;;;0BAGrI,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACd,kFACA,YAAY,8BAA8B;0CACzC;;;;;;0CAGH,6LAAC;gCAAE,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACb,uFACA,YAAY,8BAA8B;0CACzC;;;;;;;;;;;;kCAML,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACd,sGACA,YAAY,8BAA8B;0CACzC;;;;;;0CAIH,6LAAC;gCAAI,WAAU;0CACZ,mIAAA,CAAA,sBAAmB,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,sBAC1C,6LAAC;wCAEC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,sKACA,YAAY,8BAA8B;wCAE5C,OAAO;4CAAE,iBAAiB,GAAG,MAAM,QAAQ,IAAI,EAAE,CAAC;wCAAC;;0DAGnD,6LAAC;gDAAI,WAAU;0DACZ,QAAQ,IAAI;;;;;;0DAIf,6LAAC;gDAAG,WAAU;0DACX,QAAQ,KAAK;;;;;;0DAIhB,6LAAC;gDAAE,WAAU;0DACV,QAAQ,WAAW;;;;;;0DAItB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;wDAAmC;wDAC5C,QAAQ,IAAI;;;;;;;;;;;;;uCAzBf,QAAQ,EAAE;;;;;;;;;;;;;;;;kCAkCvB,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACf,+JACA,YAAY,mCAAmC;sCAE/C,cAAA,6LAAC;gCAAI,WAAU;gCAAqB,MAAK;gCAAO,QAAO;gCAAe,SAAQ;0CAC5E,cAAA,6LAAC;oCAAK,eAAc;oCAAQ,gBAAe;oCAAQ,aAAa;oCAAG,GAAE;;;;;;;;;;;;;;;;;;;;;kCAM3E,6LAAC;;0CACC,6LAAC;gCAAG,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACd,yGACA,YAAY,8BAA8B;0CACzC;;;;;;0CAIH,6LAAC;gCAAI,WAAU;0CACZ,mIAAA,CAAA,sBAAmB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC5C,6LAAC;wCAEC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,wKACA,YAAY,8BAA8B;wCAE5C,OAAO;4CAAE,iBAAiB,GAAG,OAAO,QAAQ,IAAI,EAAE,CAAC;wCAAC;;0DAGpD,6LAAC;gDAAI,WAAU;0DACZ,SAAS,IAAI;;;;;;0DAIhB,6LAAC;gDAAG,WAAU;0DACX,SAAS,KAAK;;;;;;0DAIjB,6LAAC;gDAAE,WAAU;0DACV,SAAS,WAAW;;;;;;0DAIvB,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;wDAAqC;wDAC9C,SAAS,OAAO;;;;;;;;;;;;;uCAzBnB,SAAS,EAAE;;;;;;;;;;;;;;;;kCAkCxB,6LAAC;wBAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACf,gKACA,YAAY,8BAA8B;;0CAE1C,6LAAC;gCAAG,WAAU;0CAAoD;;;;;;0CAIlE,6LAAC;gCAAI,WAAU;;kDAEb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAwC;;;;;;0DACtD,6LAAC;gDAAI,WAAU;0DACZ;oDACC;oDACA;oDACA;oDACA;oDACA;iDACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;0EAAM;;;;;;;uDAFC;;;;;;;;;;;;;;;;kDAShB,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAA0C;;;;;;0DACxD,6LAAC;gDAAI,WAAU;0DACZ;oDACC;oDACA;oDACA;oDACA;oDACA;iDACD,CAAC,GAAG,CAAC,CAAC,MAAM,sBACX,6LAAC;wDAAgB,WAAU;;0EACzB,6LAAC;gEAAI,WAAU;;;;;;0EACf,6LAAC;0EAAM;;;;;;;uDAFC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY5B;GA3MwB;KAAA", "debugId": null}}, {"offset": {"line": 1535, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/pjs/i3-d1-bestzdealai/src/components/sections/ProcessSection.js"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, useRef } from 'react';\nimport { cn } from '@/lib/utils/cn';\nimport { processSteps } from '@/lib/data/sampleData';\n\nexport default function ProcessSection() {\n  const [isVisible, setIsVisible] = useState(false);\n  const [activeStep, setActiveStep] = useState(0);\n  const sectionRef = useRef(null);\n\n  useEffect(() => {\n    const observer = new IntersectionObserver(\n      ([entry]) => {\n        if (entry.isIntersecting) {\n          setIsVisible(true);\n        }\n      },\n      { threshold: 0.2 }\n    );\n\n    if (sectionRef.current) {\n      observer.observe(sectionRef.current);\n    }\n\n    return () => observer.disconnect();\n  }, []);\n\n  // Auto-advance steps for demo\n  useEffect(() => {\n    if (isVisible) {\n      const interval = setInterval(() => {\n        setActiveStep((prev) => (prev + 1) % processSteps.length);\n      }, 4000);\n      return () => clearInterval(interval);\n    }\n  }, [isVisible]);\n\n  return (\n    <section \n      ref={sectionRef}\n      className=\"py-20 bg-gradient-to-b from-background to-surface relative overflow-hidden\"\n    >\n      {/* Background Effects */}\n      <div className=\"absolute inset-0 opacity-10\">\n        {/* Animated Connecting Lines */}\n        <svg className=\"absolute inset-0 w-full h-full\" viewBox=\"0 0 1200 800\">\n          <defs>\n            <linearGradient id=\"lineGradient\" x1=\"0%\" y1=\"0%\" x2=\"100%\" y2=\"0%\">\n              <stop offset=\"0%\" stopColor=\"var(--primary)\" />\n              <stop offset=\"100%\" stopColor=\"var(--accent)\" />\n            </linearGradient>\n          </defs>\n          <path\n            d=\"M 200 400 Q 600 200 1000 400\"\n            stroke=\"url(#lineGradient)\"\n            strokeWidth=\"2\"\n            fill=\"none\"\n            className=\"animate-pulse\"\n          />\n        </svg>\n      </div>\n\n      <div className=\"max-w-7xl mx-auto px-6\">\n        {/* Section Header */}\n        <div className=\"text-center mb-16\">\n          <h2 className={cn(\n            \"text-4xl md:text-5xl font-bold gradient-text mb-6 transition-all duration-1000\",\n            isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"\n          )}>\n            How It Works\n          </h2>\n          <p className={cn(\n            \"text-xl text-foreground/70 max-w-3xl mx-auto transition-all duration-1000 delay-200\",\n            isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"\n          )}>\n            Get the best deals in just 3 simple steps. No more endless searching or price comparisons.\n          </p>\n        </div>\n\n        {/* Process Steps - Desktop */}\n        <div className=\"hidden md:block\">\n          <div className=\"relative\">\n            {/* Connecting Line */}\n            <div className=\"absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-primary via-accent to-secondary transform -translate-y-1/2 rounded-full opacity-30\" />\n            \n            {/* Active Progress Line */}\n            <div \n              className=\"absolute top-1/2 left-0 h-1 bg-gradient-to-r from-primary to-accent transform -translate-y-1/2 rounded-full transition-all duration-1000 glow-primary\"\n              style={{ width: `${((activeStep + 1) / processSteps.length) * 100}%` }}\n            />\n\n            {/* Steps */}\n            <div className=\"relative grid grid-cols-3 gap-8\">\n              {processSteps.map((step, index) => (\n                <div\n                  key={step.id}\n                  className={cn(\n                    \"relative transition-all duration-1000\",\n                    isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"\n                  )}\n                  style={{ transitionDelay: `${400 + index * 300}ms` }}\n                >\n                  {/* Step Circle */}\n                  <div className=\"flex justify-center mb-6\">\n                    <div className={cn(\n                      \"relative w-20 h-20 rounded-full flex items-center justify-center text-2xl font-bold transition-all duration-500\",\n                      activeStep >= index \n                        ? \"bg-gradient-to-r from-primary to-accent text-white glow-primary scale-110\" \n                        : \"bg-surface border-2 border-primary/30 text-primary\"\n                    )}>\n                      {step.icon}\n                      \n                      {/* Pulse Effect for Active Step */}\n                      {activeStep === index && (\n                        <div className=\"absolute inset-0 rounded-full bg-primary/30 animate-ping\" />\n                      )}\n                    </div>\n                  </div>\n\n                  {/* Step Content */}\n                  <div className={cn(\n                    \"bg-surface/50 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-500 hover:scale-105\",\n                    activeStep === index \n                      ? \"border-primary/40 glow-primary\" \n                      : \"border-primary/20 hover:border-primary/30\"\n                  )}>\n                    <h3 className=\"text-xl font-semibold mb-3 text-center\">\n                      {step.title}\n                    </h3>\n                    <p className=\"text-foreground/70 text-center mb-4 leading-relaxed\">\n                      {step.description}\n                    </p>\n                    \n                    {/* Step Details */}\n                    <div className=\"space-y-2\">\n                      {step.details.map((detail, detailIndex) => (\n                        <div \n                          key={detailIndex}\n                          className={cn(\n                            \"flex items-center gap-2 text-sm transition-all duration-300\",\n                            activeStep === index ? \"text-primary\" : \"text-foreground/60\"\n                          )}\n                        >\n                          <div className={cn(\n                            \"w-1.5 h-1.5 rounded-full transition-colors duration-300\",\n                            activeStep === index ? \"bg-primary\" : \"bg-foreground/40\"\n                          )} />\n                          <span>{detail}</span>\n                        </div>\n                      ))}\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n        </div>\n\n        {/* Process Steps - Mobile */}\n        <div className=\"md:hidden space-y-8\">\n          {processSteps.map((step, index) => (\n            <div\n              key={step.id}\n              className={cn(\n                \"relative transition-all duration-1000\",\n                isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"\n              )}\n              style={{ transitionDelay: `${400 + index * 200}ms` }}\n            >\n              {/* Connecting Line for Mobile */}\n              {index < processSteps.length - 1 && (\n                <div className=\"absolute left-10 top-20 w-0.5 h-16 bg-gradient-to-b from-primary to-accent opacity-30\" />\n              )}\n\n              <div className=\"flex gap-4\">\n                {/* Step Circle */}\n                <div className={cn(\n                  \"flex-shrink-0 w-20 h-20 rounded-full flex items-center justify-center text-2xl font-bold transition-all duration-500\",\n                  activeStep >= index \n                    ? \"bg-gradient-to-r from-primary to-accent text-white glow-primary\" \n                    : \"bg-surface border-2 border-primary/30 text-primary\"\n                )}>\n                  {step.icon}\n                </div>\n\n                {/* Step Content */}\n                <div className={cn(\n                  \"flex-1 bg-surface/50 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-500\",\n                  activeStep === index \n                    ? \"border-primary/40 glow-primary\" \n                    : \"border-primary/20\"\n                )}>\n                  <h3 className=\"text-xl font-semibold mb-3\">\n                    {step.title}\n                  </h3>\n                  <p className=\"text-foreground/70 mb-4 leading-relaxed\">\n                    {step.description}\n                  </p>\n                  \n                  {/* Step Details */}\n                  <div className=\"space-y-2\">\n                    {step.details.map((detail, detailIndex) => (\n                      <div \n                        key={detailIndex}\n                        className={cn(\n                          \"flex items-center gap-2 text-sm transition-all duration-300\",\n                          activeStep === index ? \"text-primary\" : \"text-foreground/60\"\n                        )}\n                      >\n                        <div className={cn(\n                          \"w-1.5 h-1.5 rounded-full transition-colors duration-300\",\n                          activeStep === index ? \"bg-primary\" : \"bg-foreground/40\"\n                        )} />\n                        <span>{detail}</span>\n                      </div>\n                    ))}\n                  </div>\n                </div>\n              </div>\n            </div>\n          ))}\n        </div>\n\n        {/* Interactive Demo Preview */}\n        <div className={cn(\n          \"mt-16 bg-gradient-to-r from-surface/50 to-surface-light/50 backdrop-blur-sm rounded-2xl p-8 border border-primary/20 transition-all duration-1000 delay-1000\",\n          isVisible ? \"opacity-100 translate-y-0\" : \"opacity-0 translate-y-10\"\n        )}>\n          <h4 className=\"text-2xl font-bold text-center mb-8 gradient-text\">\n            See Step {activeStep + 1} in Action\n          </h4>\n          \n          <div className=\"max-w-2xl mx-auto\">\n            {activeStep === 0 && (\n              <div className=\"bg-background/50 rounded-lg p-6\">\n                <div className=\"text-center mb-4\">\n                  <div className=\"text-4xl mb-2\">📝</div>\n                  <h5 className=\"font-semibold\">Creating Your Request</h5>\n                </div>\n                <div className=\"space-y-3 text-left\">\n                  <div className=\"bg-surface/50 rounded p-3\">\n                    <div className=\"text-sm text-foreground/60\">Product:</div>\n                    <div className=\"font-medium\">MacBook Pro 14-inch</div>\n                  </div>\n                  <div className=\"bg-surface/50 rounded p-3\">\n                    <div className=\"text-sm text-foreground/60\">Budget:</div>\n                    <div className=\"font-medium\">$1,800 - $2,200</div>\n                  </div>\n                  <div className=\"bg-surface/50 rounded p-3\">\n                    <div className=\"text-sm text-foreground/60\">Location:</div>\n                    <div className=\"font-medium\">San Francisco, CA</div>\n                  </div>\n                </div>\n              </div>\n            )}\n\n            {activeStep === 1 && (\n              <div className=\"bg-background/50 rounded-lg p-6\">\n                <div className=\"text-center mb-4\">\n                  <div className=\"text-4xl mb-2\">🏆</div>\n                  <h5 className=\"font-semibold\">Sellers Responding</h5>\n                </div>\n                <div className=\"space-y-2\">\n                  {[\n                    { name: \"Apple Store SF\", price: \"$1,999\", status: \"New offer\" },\n                    { name: \"Best Buy\", price: \"$2,049\", status: \"Negotiating\" },\n                    { name: \"Local Tech Shop\", price: \"$1,899\", status: \"Best price!\" }\n                  ].map((offer, index) => (\n                    <div key={index} className=\"bg-surface/50 rounded p-3 flex justify-between items-center animate-pulse-glow\" style={{ animationDelay: `${index * 0.5}s` }}>\n                      <span>{offer.name}</span>\n                      <div className=\"text-right\">\n                        <div className=\"font-bold text-success\">{offer.price}</div>\n                        <div className=\"text-xs text-primary\">{offer.status}</div>\n                      </div>\n                    </div>\n                  ))}\n                </div>\n              </div>\n            )}\n\n            {activeStep === 2 && (\n              <div className=\"bg-background/50 rounded-lg p-6\">\n                <div className=\"text-center mb-4\">\n                  <div className=\"text-4xl mb-2\">💰</div>\n                  <h5 className=\"font-semibold\">Deal Completed</h5>\n                </div>\n                <div className=\"bg-success/20 rounded-lg p-4 border border-success/30\">\n                  <div className=\"text-center\">\n                    <div className=\"text-success font-bold text-lg\">✅ Deal Accepted!</div>\n                    <div className=\"text-sm text-foreground/70 mt-2\">\n                      MacBook Pro 14-inch from Local Tech Shop\n                    </div>\n                    <div className=\"text-lg font-bold text-success mt-2\">$1,899</div>\n                    <div className=\"text-sm text-success mt-1\">\n                      💰 Saved $150 compared to retail\n                    </div>\n                  </div>\n                </div>\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    </section>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;;;AAJA;;;;AAMe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAM,AAAD,EAAE;IAE1B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,MAAM,WAAW,IAAI;4CACnB,CAAC,CAAC,MAAM;oBACN,IAAI,MAAM,cAAc,EAAE;wBACxB,aAAa;oBACf;gBACF;2CACA;gBAAE,WAAW;YAAI;YAGnB,IAAI,WAAW,OAAO,EAAE;gBACtB,SAAS,OAAO,CAAC,WAAW,OAAO;YACrC;YAEA;4CAAO,IAAM,SAAS,UAAU;;QAClC;mCAAG,EAAE;IAEL,8BAA8B;IAC9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;oCAAE;YACR,IAAI,WAAW;gBACb,MAAM,WAAW;yDAAY;wBAC3B;iEAAc,CAAC,OAAS,CAAC,OAAO,CAAC,IAAI,mIAAA,CAAA,eAAY,CAAC,MAAM;;oBAC1D;wDAAG;gBACH;gDAAO,IAAM,cAAc;;YAC7B;QACF;mCAAG;QAAC;KAAU;IAEd,qBACE,6LAAC;QACC,KAAK;QACL,WAAU;;0BAGV,6LAAC;gBAAI,WAAU;0BAEb,cAAA,6LAAC;oBAAI,WAAU;oBAAiC,SAAQ;;sCACtD,6LAAC;sCACC,cAAA,6LAAC;gCAAe,IAAG;gCAAe,IAAG;gCAAK,IAAG;gCAAK,IAAG;gCAAO,IAAG;;kDAC7D,6LAAC;wCAAK,QAAO;wCAAK,WAAU;;;;;;kDAC5B,6LAAC;wCAAK,QAAO;wCAAO,WAAU;;;;;;;;;;;;;;;;;sCAGlC,6LAAC;4BACC,GAAE;4BACF,QAAO;4BACP,aAAY;4BACZ,MAAK;4BACL,WAAU;;;;;;;;;;;;;;;;;0BAKhB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACd,kFACA,YAAY,8BAA8B;0CACzC;;;;;;0CAGH,6LAAC;gCAAE,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACb,uFACA,YAAY,8BAA8B;0CACzC;;;;;;;;;;;;kCAML,6LAAC;wBAAI,WAAU;kCACb,cAAA,6LAAC;4BAAI,WAAU;;8CAEb,6LAAC;oCAAI,WAAU;;;;;;8CAGf,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,AAAC,CAAC,aAAa,CAAC,IAAI,mIAAA,CAAA,eAAY,CAAC,MAAM,GAAI,IAAI,CAAC,CAAC;oCAAC;;;;;;8CAIvE,6LAAC;oCAAI,WAAU;8CACZ,mIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;4CAEC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,yCACA,YAAY,8BAA8B;4CAE5C,OAAO;gDAAE,iBAAiB,GAAG,MAAM,QAAQ,IAAI,EAAE,CAAC;4CAAC;;8DAGnD,6LAAC;oDAAI,WAAU;8DACb,cAAA,6LAAC;wDAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACf,mHACA,cAAc,QACV,8EACA;;4DAEH,KAAK,IAAI;4DAGT,eAAe,uBACd,6LAAC;gEAAI,WAAU;;;;;;;;;;;;;;;;;8DAMrB,6LAAC;oDAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACf,qGACA,eAAe,QACX,mCACA;;sEAEJ,6LAAC;4DAAG,WAAU;sEACX,KAAK,KAAK;;;;;;sEAEb,6LAAC;4DAAE,WAAU;sEACV,KAAK,WAAW;;;;;;sEAInB,6LAAC;4DAAI,WAAU;sEACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,6LAAC;oEAEC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+DACA,eAAe,QAAQ,iBAAiB;;sFAG1C,6LAAC;4EAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACf,2DACA,eAAe,QAAQ,eAAe;;;;;;sFAExC,6LAAC;sFAAM;;;;;;;mEAVF;;;;;;;;;;;;;;;;;2CA1CR,KAAK,EAAE;;;;;;;;;;;;;;;;;;;;;kCAgEtB,6LAAC;wBAAI,WAAU;kCACZ,mIAAA,CAAA,eAAY,CAAC,GAAG,CAAC,CAAC,MAAM,sBACvB,6LAAC;gCAEC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,yCACA,YAAY,8BAA8B;gCAE5C,OAAO;oCAAE,iBAAiB,GAAG,MAAM,QAAQ,IAAI,EAAE,CAAC;gCAAC;;oCAGlD,QAAQ,mIAAA,CAAA,eAAY,CAAC,MAAM,GAAG,mBAC7B,6LAAC;wCAAI,WAAU;;;;;;kDAGjB,6LAAC;wCAAI,WAAU;;0DAEb,6LAAC;gDAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACf,wHACA,cAAc,QACV,oEACA;0DAEH,KAAK,IAAI;;;;;;0DAIZ,6LAAC;gDAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACf,4FACA,eAAe,QACX,mCACA;;kEAEJ,6LAAC;wDAAG,WAAU;kEACX,KAAK,KAAK;;;;;;kEAEb,6LAAC;wDAAE,WAAU;kEACV,KAAK,WAAW;;;;;;kEAInB,6LAAC;wDAAI,WAAU;kEACZ,KAAK,OAAO,CAAC,GAAG,CAAC,CAAC,QAAQ,4BACzB,6LAAC;gEAEC,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACV,+DACA,eAAe,QAAQ,iBAAiB;;kFAG1C,6LAAC;wEAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACf,2DACA,eAAe,QAAQ,eAAe;;;;;;kFAExC,6LAAC;kFAAM;;;;;;;+DAVF;;;;;;;;;;;;;;;;;;;;;;;+BAzCV,KAAK,EAAE;;;;;;;;;;kCA8DlB,6LAAC;wBAAI,WAAW,CAAA,GAAA,4HAAA,CAAA,KAAE,AAAD,EACf,gKACA,YAAY,8BAA8B;;0CAE1C,6LAAC;gCAAG,WAAU;;oCAAoD;oCACtD,aAAa;oCAAE;;;;;;;0CAG3B,6LAAC;gCAAI,WAAU;;oCACZ,eAAe,mBACd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,6LAAC;wDAAG,WAAU;kEAAgB;;;;;;;;;;;;0DAEhC,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAA6B;;;;;;0EAC5C,6LAAC;gEAAI,WAAU;0EAAc;;;;;;;;;;;;kEAE/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAA6B;;;;;;0EAC5C,6LAAC;gEAAI,WAAU;0EAAc;;;;;;;;;;;;kEAE/B,6LAAC;wDAAI,WAAU;;0EACb,6LAAC;gEAAI,WAAU;0EAA6B;;;;;;0EAC5C,6LAAC;gEAAI,WAAU;0EAAc;;;;;;;;;;;;;;;;;;;;;;;;oCAMpC,eAAe,mBACd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,6LAAC;wDAAG,WAAU;kEAAgB;;;;;;;;;;;;0DAEhC,6LAAC;gDAAI,WAAU;0DACZ;oDACC;wDAAE,MAAM;wDAAkB,OAAO;wDAAU,QAAQ;oDAAY;oDAC/D;wDAAE,MAAM;wDAAY,OAAO;wDAAU,QAAQ;oDAAc;oDAC3D;wDAAE,MAAM;wDAAmB,OAAO;wDAAU,QAAQ;oDAAc;iDACnE,CAAC,GAAG,CAAC,CAAC,OAAO,sBACZ,6LAAC;wDAAgB,WAAU;wDAAiF,OAAO;4DAAE,gBAAgB,GAAG,QAAQ,IAAI,CAAC,CAAC;wDAAC;;0EACrJ,6LAAC;0EAAM,MAAM,IAAI;;;;;;0EACjB,6LAAC;gEAAI,WAAU;;kFACb,6LAAC;wEAAI,WAAU;kFAA0B,MAAM,KAAK;;;;;;kFACpD,6LAAC;wEAAI,WAAU;kFAAwB,MAAM,MAAM;;;;;;;;;;;;;uDAJ7C;;;;;;;;;;;;;;;;oCAYjB,eAAe,mBACd,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEAAgB;;;;;;kEAC/B,6LAAC;wDAAG,WAAU;kEAAgB;;;;;;;;;;;;0DAEhC,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAI,WAAU;;sEACb,6LAAC;4DAAI,WAAU;sEAAiC;;;;;;sEAChD,6LAAC;4DAAI,WAAU;sEAAkC;;;;;;sEAGjD,6LAAC;4DAAI,WAAU;sEAAsC;;;;;;sEACrD,6LAAC;4DAAI,WAAU;sEAA4B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAY/D;GA5SwB;KAAA", "debugId": null}}]}