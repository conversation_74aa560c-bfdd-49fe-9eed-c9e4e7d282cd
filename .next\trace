[{"name": "hot-reloader", "duration": 63, "timestamp": 4909562345, "id": 3, "tags": {"version": "15.3.2"}, "startTime": 1748506001811, "traceId": "467f66d36300f933"}, {"name": "setup-dev-bundler", "duration": 1351513, "timestamp": 4908564830, "id": 2, "parentId": 1, "tags": {}, "startTime": 1748506000813, "traceId": "467f66d36300f933"}, {"name": "run-instrumentation-hook", "duration": 16, "timestamp": 4909952122, "id": 4, "parentId": 1, "tags": {}, "startTime": 1748506002200, "traceId": "467f66d36300f933"}, {"name": "start-dev-server", "duration": 1751285, "timestamp": 4908208931, "id": 1, "tags": {"cpus": "16", "platform": "win32", "memory.freeMem": "51077603328", "memory.totalMem": "68560273408", "memory.heapSizeLimit": "34481373184", "memory.rss": "170885120", "memory.heapTotal": "112168960", "memory.heapUsed": "58005056"}, "startTime": 1748506000457, "traceId": "467f66d36300f933"}, {"name": "compile-path", "duration": 1315449, "timestamp": 4949919249, "id": 7, "tags": {"trigger": "/"}, "startTime": 1748506042167, "traceId": "467f66d36300f933"}, {"name": "ensure-page", "duration": 1316772, "timestamp": 4949918376, "id": 6, "parentId": 3, "tags": {"inputPage": "/page"}, "startTime": 1748506042167, "traceId": "467f66d36300f933"}]