@import "tailwindcss";

/* BestzDealAi - Futuristic AI Design System */
:root {
  /* Core Colors */
  --background: #0a0a0f;
  --foreground: #ffffff;
  --surface: #1a1a2e;
  --surface-light: #16213e;

  /* AI-Inspired Accent Colors */
  --primary: #00d4ff;
  --primary-dark: #0099cc;
  --secondary: #ff6b35;
  --accent: #7c3aed;
  --success: #10b981;
  --warning: #f59e0b;
  --error: #ef4444;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #00d4ff 0%, #7c3aed 100%);
  --gradient-secondary: linear-gradient(135deg, #ff6b35 0%, #f59e0b 100%);
  --gradient-surface: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);

  /* Neon Glows */
  --glow-primary: 0 0 20px rgba(0, 212, 255, 0.3);
  --glow-secondary: 0 0 20px rgba(255, 107, 53, 0.3);
  --glow-accent: 0 0 20px rgba(124, 58, 237, 0.3);

  /* Typography */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Spacing & Layout */
  --border-radius: 12px;
  --border-radius-lg: 20px;
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4px 16px rgba(0, 0, 0, 0.2);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.3);
}

@theme inline {
  /* Tailwind CSS v4 Theme Configuration */
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-surface: var(--surface);
  --color-surface-light: var(--surface-light);
  --color-primary: var(--primary);
  --color-primary-dark: var(--primary-dark);
  --color-secondary: var(--secondary);
  --color-accent: var(--accent);
  --color-success: var(--success);
  --color-warning: var(--warning);
  --color-error: var(--error);

  /* Font Configuration */
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);

  /* Custom Utilities */
  --animate-float: float 6s ease-in-out infinite;
  --animate-pulse-glow: pulse-glow 2s ease-in-out infinite alternate;
  --animate-matrix: matrix 20s linear infinite;
}

/* Light Mode Override */
@media (prefers-color-scheme: light) {
  :root {
    --background: #ffffff;
    --foreground: #0a0a0f;
    --surface: #f8fafc;
    --surface-light: #f1f5f9;
  }
}

/* Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html {
  scroll-behavior: smooth;
  overflow-x: hidden;
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, -apple-system, sans-serif;
  line-height: 1.6;
  overflow-x: hidden;
  min-height: 100vh;
  position: relative;
}

/* Custom Animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse-glow {
  0% { box-shadow: var(--glow-primary); }
  100% { box-shadow: 0 0 40px rgba(0, 212, 255, 0.6); }
}

@keyframes matrix {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(100vh); }
}

@keyframes typing {
  from { width: 0; }
  to { width: 100%; }
}

@keyframes blink {
  0%, 50% { border-color: transparent; }
  51%, 100% { border-color: var(--primary); }
}

/* Utility Classes */
.gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glow-primary {
  box-shadow: var(--glow-primary);
}

.glow-secondary {
  box-shadow: var(--glow-secondary);
}

.glow-accent {
  box-shadow: var(--glow-accent);
}

.animate-float {
  animation: var(--animate-float);
}

.animate-pulse-glow {
  animation: var(--animate-pulse-glow);
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--surface);
}

::-webkit-scrollbar-thumb {
  background: var(--primary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-dark);
}

/* Selection Styling */
::selection {
  background: var(--primary);
  color: var(--background);
}

/* Focus Styles */
:focus-visible {
  outline: 2px solid var(--primary);
  outline-offset: 2px;
}
