import { Geist, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
  variable: "--font-geist-sans",
  subsets: ["latin"],
});

const geistMono = Geist_Mono({
  variable: "--font-geist-mono",
  subsets: ["latin"],
});

export const metadata = {
  title: "BestzDealAi - AI-Powered Reverse Marketplace",
  description: "Post what you want, sellers compete to offer the best deal. One post — many offers. The deal comes to you.",
  keywords: "marketplace, deals, AI, reverse auction, best prices, local sellers, online shopping",
  authors: [{ name: "BestzDealAi Team" }],
  creator: "BestzDealAi",
  publisher: "BestzDealAi",
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  metadataBase: new URL('https://bestzdealai.com'),
  alternates: {
    canonical: '/',
  },
  openGraph: {
    title: "BestzDealAi - AI-Powered Reverse Marketplace",
    description: "Post what you want, sellers compete to offer the best deal. One post — many offers.",
    url: 'https://bestzdealai.com',
    siteName: 'BestzDealAi',
    images: [
      {
        url: 'https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp',
        width: 1200,
        height: 630,
        alt: 'BestzDealAi Logo',
      },
    ],
    locale: 'en_US',
    type: 'website',
  },
  twitter: {
    card: 'summary_large_image',
    title: "BestzDealAi - AI-Powered Reverse Marketplace",
    description: "Post what you want, sellers compete to offer the best deal. One post — many offers.",
    images: ['https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp'],
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  icons: {
    icon: 'https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp',
    shortcut: 'https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp',
    apple: 'https://raw.githubusercontent.com/HunterHo07/Portfolio_1/refs/heads/main/images/logo.webp',
  },
};

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body
        className={`${geistSans.variable} ${geistMono.variable} antialiased`}
      >
        {children}
      </body>
    </html>
  );
}
