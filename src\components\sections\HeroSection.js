'use client';

import { useState, useEffect } from 'react';
import { cn } from '@/lib/utils/cn';
import { heroData } from '@/lib/data/sampleData';

export default function HeroSection() {
  const [typedText, setTypedText] = useState('');
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isDeleting, setIsDeleting] = useState(false);
  
  const phrases = [
    "You post it. They deal it.",
    "The deal comes to you.",
    "Smart shopping starts here.",
    "Why search when sellers find you?"
  ];

  useEffect(() => {
    const currentPhrase = phrases[currentIndex];
    const timeout = setTimeout(() => {
      if (!isDeleting) {
        if (typedText.length < currentPhrase.length) {
          setTypedText(currentPhrase.slice(0, typedText.length + 1));
        } else {
          setTimeout(() => setIsDeleting(true), 2000);
        }
      } else {
        if (typedText.length > 0) {
          setTypedText(typedText.slice(0, -1));
        } else {
          setIsDeleting(false);
          setCurrentIndex((prev) => (prev + 1) % phrases.length);
        }
      }
    }, isDeleting ? 50 : 100);

    return () => clearTimeout(timeout);
  }, [typedText, currentIndex, isDeleting, phrases]);

  return (
    <section className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-background via-surface to-surface-light">
      {/* Animated Background Elements */}
      <div className="absolute inset-0 overflow-hidden">
        {/* Floating Geometric Shapes */}
        <div className="absolute top-20 left-10 w-20 h-20 bg-primary/20 rounded-full animate-float" 
             style={{ animationDelay: '0s' }} />
        <div className="absolute top-40 right-20 w-16 h-16 bg-secondary/20 rounded-lg rotate-45 animate-float" 
             style={{ animationDelay: '1s' }} />
        <div className="absolute bottom-40 left-20 w-24 h-24 bg-accent/20 rounded-full animate-float" 
             style={{ animationDelay: '2s' }} />
        <div className="absolute bottom-20 right-10 w-18 h-18 bg-primary/20 rounded-lg rotate-12 animate-float" 
             style={{ animationDelay: '3s' }} />
        
        {/* Matrix Rain Effect */}
        <div className="absolute inset-0 opacity-10">
          {Array.from({ length: 20 }).map((_, i) => (
            <div
              key={i}
              className="absolute text-primary font-mono text-sm animate-matrix"
              style={{
                left: `${Math.random() * 100}%`,
                animationDelay: `${Math.random() * 20}s`,
                animationDuration: `${15 + Math.random() * 10}s`
              }}
            >
              {Array.from({ length: 10 }).map((_, j) => (
                <div key={j} className="mb-2">
                  {Math.random() > 0.5 ? '1' : '0'}
                </div>
              ))}
            </div>
          ))}
        </div>
      </div>

      {/* Main Content */}
      <div className="relative z-10 max-w-6xl mx-auto px-6 text-center">
        {/* Logo and Brand */}
        <div className="mb-8">
          <div className="inline-flex items-center gap-4 mb-4">
            {/* Custom SVG Logo */}
            <div className="w-16 h-16 bg-gradient-to-br from-primary to-accent rounded-xl flex items-center justify-center glow-primary">
              <svg viewBox="0 0 24 24" className="w-10 h-10 text-white" fill="currentColor">
                <path d="M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5"/>
              </svg>
            </div>
            <h1 className="text-5xl md:text-7xl font-bold gradient-text">
              {heroData.title}
            </h1>
          </div>
          <p className="text-xl md:text-2xl text-foreground/80 font-medium">
            {heroData.subtitle}
          </p>
        </div>

        {/* Typing Animation */}
        <div className="mb-12">
          <div className="text-2xl md:text-4xl font-bold text-primary min-h-[3rem] flex items-center justify-center">
            <span className="border-r-2 border-primary animate-pulse pr-1">
              {typedText}
            </span>
          </div>
        </div>

        {/* Description */}
        <p className="text-lg md:text-xl text-foreground/70 max-w-3xl mx-auto mb-12 leading-relaxed">
          {heroData.description}
        </p>

        {/* CTA Buttons */}
        <div className="flex flex-col sm:flex-row gap-6 justify-center items-center mb-16">
          <button className="group relative px-8 py-4 bg-gradient-to-r from-primary to-accent text-white font-semibold rounded-xl text-lg transition-all duration-300 hover:scale-105 glow-primary hover:shadow-2xl">
            <span className="relative z-10">{heroData.cta.primary}</span>
            <div className="absolute inset-0 bg-gradient-to-r from-primary-dark to-accent rounded-xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
          </button>
          
          <button className="group px-8 py-4 border-2 border-primary text-primary font-semibold rounded-xl text-lg transition-all duration-300 hover:bg-primary hover:text-background hover:scale-105">
            {heroData.cta.secondary}
          </button>
        </div>

        {/* Mini Demo Loop */}
        <div className="relative max-w-4xl mx-auto">
          <div className="bg-surface/50 backdrop-blur-sm rounded-2xl p-8 border border-primary/20 glow-primary">
            <h3 className="text-xl font-semibold mb-6 text-primary">See It In Action</h3>
            
            {/* Simulated Demo Interface */}
            <div className="grid md:grid-cols-2 gap-6">
              {/* Buyer Side */}
              <div className="space-y-4">
                <h4 className="font-medium text-foreground/80">1. You Post</h4>
                <div className="bg-background/50 rounded-lg p-4 text-left">
                  <div className="text-sm text-foreground/60 mb-2">Looking for:</div>
                  <div className="font-medium">iPhone 15 Pro</div>
                  <div className="text-sm text-foreground/60 mt-2">Budget: $800-900</div>
                  <div className="text-sm text-foreground/60">Location: San Francisco</div>
                </div>
              </div>

              {/* Seller Side */}
              <div className="space-y-4">
                <h4 className="font-medium text-foreground/80">2. Sellers Compete</h4>
                <div className="space-y-2">
                  {[
                    { name: "TechHub SF", price: "$849", rating: "4.8★" },
                    { name: "Mobile World", price: "$829", rating: "4.6★" },
                    { name: "Best Buy", price: "$899", rating: "4.7★" }
                  ].map((offer, index) => (
                    <div 
                      key={index}
                      className="bg-background/50 rounded-lg p-3 flex justify-between items-center text-sm animate-pulse-glow"
                      style={{ animationDelay: `${index * 0.5}s` }}
                    >
                      <span>{offer.name}</span>
                      <div className="text-right">
                        <div className="font-bold text-success">{offer.price}</div>
                        <div className="text-xs text-foreground/60">{offer.rating}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* Result */}
            <div className="mt-6 p-4 bg-success/20 rounded-lg border border-success/30">
              <div className="text-success font-semibold">✅ Best Deal Found!</div>
              <div className="text-sm text-foreground/70 mt-1">
                Saved $70 compared to retail price
              </div>
            </div>
          </div>
        </div>

        {/* Scroll Indicator */}
        <div className="absolute bottom-8 left-1/2 transform -translate-x-1/2 animate-bounce">
          <div className="w-6 h-10 border-2 border-primary rounded-full flex justify-center">
            <div className="w-1 h-3 bg-primary rounded-full mt-2 animate-pulse" />
          </div>
        </div>
      </div>
    </section>
  );
}
