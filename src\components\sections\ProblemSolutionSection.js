'use client';

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils/cn';
import { problemSolutionData } from '@/lib/data/sampleData';

export default function ProblemSolutionSection() {
  const [isVisible, setIsVisible] = useState(false);
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section 
      ref={sectionRef}
      className="py-20 bg-gradient-to-b from-surface-light to-background relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-5">
        <div className="absolute top-10 left-10 w-32 h-32 bg-secondary rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-10 right-10 w-40 h-40 bg-accent rounded-full blur-3xl animate-pulse" style={{ animationDelay: '1s' }} />
      </div>

      <div className="max-w-7xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className={cn(
            "text-4xl md:text-5xl font-bold gradient-text mb-6 transition-all duration-1000",
            isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
          )}>
            The Problem We Solve
          </h2>
          <p className={cn(
            "text-xl text-foreground/70 max-w-3xl mx-auto transition-all duration-1000 delay-200",
            isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
          )}>
            Traditional shopping is broken. We're fixing it with AI-powered reverse marketplace technology.
          </p>
        </div>

        {/* Problems Section */}
        <div className="mb-20">
          <h3 className={cn(
            "text-2xl md:text-3xl font-bold text-center mb-12 text-error transition-all duration-1000 delay-300",
            isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
          )}>
            😤 Current Shopping Problems
          </h3>
          
          <div className="grid md:grid-cols-3 gap-8">
            {problemSolutionData.problems.map((problem, index) => (
              <div
                key={problem.id}
                className={cn(
                  "group bg-surface/50 backdrop-blur-sm rounded-2xl p-6 border border-error/20 hover:border-error/40 transition-all duration-500 hover:scale-105 hover:glow-secondary",
                  isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
                )}
                style={{ transitionDelay: `${400 + index * 200}ms` }}
              >
                {/* Problem Icon */}
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {problem.icon}
                </div>
                
                {/* Problem Title */}
                <h4 className="text-xl font-semibold mb-3 text-foreground group-hover:text-error transition-colors duration-300">
                  {problem.title}
                </h4>
                
                {/* Problem Description */}
                <p className="text-foreground/70 mb-4 leading-relaxed">
                  {problem.description}
                </p>
                
                {/* Problem Stat */}
                <div className="bg-error/10 rounded-lg p-3 border border-error/20">
                  <div className="text-error font-semibold text-sm">
                    📊 {problem.stat}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Transition Arrow */}
        <div className="text-center mb-20">
          <div className={cn(
            "inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-primary to-accent rounded-full glow-primary transition-all duration-1000 delay-1000",
            isVisible ? "opacity-100 scale-100 rotate-0" : "opacity-0 scale-50 rotate-180"
          )}>
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </div>
        </div>

        {/* Solutions Section */}
        <div>
          <h3 className={cn(
            "text-2xl md:text-3xl font-bold text-center mb-12 text-success transition-all duration-1000 delay-1200",
            isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
          )}>
            ✨ Our Smart Solutions
          </h3>
          
          <div className="grid md:grid-cols-3 gap-8">
            {problemSolutionData.solutions.map((solution, index) => (
              <div
                key={solution.id}
                className={cn(
                  "group bg-surface/50 backdrop-blur-sm rounded-2xl p-6 border border-success/20 hover:border-success/40 transition-all duration-500 hover:scale-105 hover:glow-primary",
                  isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
                )}
                style={{ transitionDelay: `${1300 + index * 200}ms` }}
              >
                {/* Solution Icon */}
                <div className="text-4xl mb-4 group-hover:scale-110 transition-transform duration-300">
                  {solution.icon}
                </div>
                
                {/* Solution Title */}
                <h4 className="text-xl font-semibold mb-3 text-foreground group-hover:text-success transition-colors duration-300">
                  {solution.title}
                </h4>
                
                {/* Solution Description */}
                <p className="text-foreground/70 mb-4 leading-relaxed">
                  {solution.description}
                </p>
                
                {/* Solution Benefit */}
                <div className="bg-success/10 rounded-lg p-3 border border-success/20">
                  <div className="text-success font-semibold text-sm">
                    🎯 {solution.benefit}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Comparison Chart */}
        <div className={cn(
          "mt-20 bg-gradient-to-r from-surface/50 to-surface-light/50 backdrop-blur-sm rounded-2xl p-8 border border-primary/20 transition-all duration-1000 delay-1900",
          isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
        )}>
          <h4 className="text-2xl font-bold text-center mb-8 gradient-text">
            Traditional vs BestzDealAi
          </h4>
          
          <div className="grid md:grid-cols-2 gap-8">
            {/* Traditional Way */}
            <div className="space-y-4">
              <h5 className="text-lg font-semibold text-error mb-4">❌ Traditional Shopping</h5>
              <div className="space-y-3">
                {[
                  "Search multiple websites manually",
                  "Compare prices one by one",
                  "No negotiation power",
                  "Miss local deals",
                  "Time-consuming process"
                ].map((item, index) => (
                  <div key={index} className="flex items-center gap-3 text-foreground/70">
                    <div className="w-2 h-2 bg-error rounded-full" />
                    <span>{item}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* BestzDealAi Way */}
            <div className="space-y-4">
              <h5 className="text-lg font-semibold text-success mb-4">✅ BestzDealAi Way</h5>
              <div className="space-y-3">
                {[
                  "Post once, get multiple offers",
                  "AI ranks best value deals",
                  "Sellers compete for your business",
                  "Discover local hidden gems",
                  "Save time and money"
                ].map((item, index) => (
                  <div key={index} className="flex items-center gap-3 text-foreground/70">
                    <div className="w-2 h-2 bg-success rounded-full" />
                    <span>{item}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
