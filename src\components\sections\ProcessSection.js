'use client';

import { useState, useEffect, useRef } from 'react';
import { cn } from '@/lib/utils/cn';
import { processSteps } from '@/lib/data/sampleData';

export default function ProcessSection() {
  const [isVisible, setIsVisible] = useState(false);
  const [activeStep, setActiveStep] = useState(0);
  const sectionRef = useRef(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
        }
      },
      { threshold: 0.2 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  // Auto-advance steps for demo
  useEffect(() => {
    if (isVisible) {
      const interval = setInterval(() => {
        setActiveStep((prev) => (prev + 1) % processSteps.length);
      }, 4000);
      return () => clearInterval(interval);
    }
  }, [isVisible]);

  return (
    <section 
      ref={sectionRef}
      className="py-20 bg-gradient-to-b from-background to-surface relative overflow-hidden"
    >
      {/* Background Effects */}
      <div className="absolute inset-0 opacity-10">
        {/* Animated Connecting Lines */}
        <svg className="absolute inset-0 w-full h-full" viewBox="0 0 1200 800">
          <defs>
            <linearGradient id="lineGradient" x1="0%" y1="0%" x2="100%" y2="0%">
              <stop offset="0%" stopColor="var(--primary)" />
              <stop offset="100%" stopColor="var(--accent)" />
            </linearGradient>
          </defs>
          <path
            d="M 200 400 Q 600 200 1000 400"
            stroke="url(#lineGradient)"
            strokeWidth="2"
            fill="none"
            className="animate-pulse"
          />
        </svg>
      </div>

      <div className="max-w-7xl mx-auto px-6">
        {/* Section Header */}
        <div className="text-center mb-16">
          <h2 className={cn(
            "text-4xl md:text-5xl font-bold gradient-text mb-6 transition-all duration-1000",
            isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
          )}>
            How It Works
          </h2>
          <p className={cn(
            "text-xl text-foreground/70 max-w-3xl mx-auto transition-all duration-1000 delay-200",
            isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
          )}>
            Get the best deals in just 3 simple steps. No more endless searching or price comparisons.
          </p>
        </div>

        {/* Process Steps - Desktop */}
        <div className="hidden md:block">
          <div className="relative">
            {/* Connecting Line */}
            <div className="absolute top-1/2 left-0 right-0 h-1 bg-gradient-to-r from-primary via-accent to-secondary transform -translate-y-1/2 rounded-full opacity-30" />
            
            {/* Active Progress Line */}
            <div 
              className="absolute top-1/2 left-0 h-1 bg-gradient-to-r from-primary to-accent transform -translate-y-1/2 rounded-full transition-all duration-1000 glow-primary"
              style={{ width: `${((activeStep + 1) / processSteps.length) * 100}%` }}
            />

            {/* Steps */}
            <div className="relative grid grid-cols-3 gap-8">
              {processSteps.map((step, index) => (
                <div
                  key={step.id}
                  className={cn(
                    "relative transition-all duration-1000",
                    isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
                  )}
                  style={{ transitionDelay: `${400 + index * 300}ms` }}
                >
                  {/* Step Circle */}
                  <div className="flex justify-center mb-6">
                    <div className={cn(
                      "relative w-20 h-20 rounded-full flex items-center justify-center text-2xl font-bold transition-all duration-500",
                      activeStep >= index 
                        ? "bg-gradient-to-r from-primary to-accent text-white glow-primary scale-110" 
                        : "bg-surface border-2 border-primary/30 text-primary"
                    )}>
                      {step.icon}
                      
                      {/* Pulse Effect for Active Step */}
                      {activeStep === index && (
                        <div className="absolute inset-0 rounded-full bg-primary/30 animate-ping" />
                      )}
                    </div>
                  </div>

                  {/* Step Content */}
                  <div className={cn(
                    "bg-surface/50 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-500 hover:scale-105",
                    activeStep === index 
                      ? "border-primary/40 glow-primary" 
                      : "border-primary/20 hover:border-primary/30"
                  )}>
                    <h3 className="text-xl font-semibold mb-3 text-center">
                      {step.title}
                    </h3>
                    <p className="text-foreground/70 text-center mb-4 leading-relaxed">
                      {step.description}
                    </p>
                    
                    {/* Step Details */}
                    <div className="space-y-2">
                      {step.details.map((detail, detailIndex) => (
                        <div 
                          key={detailIndex}
                          className={cn(
                            "flex items-center gap-2 text-sm transition-all duration-300",
                            activeStep === index ? "text-primary" : "text-foreground/60"
                          )}
                        >
                          <div className={cn(
                            "w-1.5 h-1.5 rounded-full transition-colors duration-300",
                            activeStep === index ? "bg-primary" : "bg-foreground/40"
                          )} />
                          <span>{detail}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Process Steps - Mobile */}
        <div className="md:hidden space-y-8">
          {processSteps.map((step, index) => (
            <div
              key={step.id}
              className={cn(
                "relative transition-all duration-1000",
                isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
              )}
              style={{ transitionDelay: `${400 + index * 200}ms` }}
            >
              {/* Connecting Line for Mobile */}
              {index < processSteps.length - 1 && (
                <div className="absolute left-10 top-20 w-0.5 h-16 bg-gradient-to-b from-primary to-accent opacity-30" />
              )}

              <div className="flex gap-4">
                {/* Step Circle */}
                <div className={cn(
                  "flex-shrink-0 w-20 h-20 rounded-full flex items-center justify-center text-2xl font-bold transition-all duration-500",
                  activeStep >= index 
                    ? "bg-gradient-to-r from-primary to-accent text-white glow-primary" 
                    : "bg-surface border-2 border-primary/30 text-primary"
                )}>
                  {step.icon}
                </div>

                {/* Step Content */}
                <div className={cn(
                  "flex-1 bg-surface/50 backdrop-blur-sm rounded-2xl p-6 border transition-all duration-500",
                  activeStep === index 
                    ? "border-primary/40 glow-primary" 
                    : "border-primary/20"
                )}>
                  <h3 className="text-xl font-semibold mb-3">
                    {step.title}
                  </h3>
                  <p className="text-foreground/70 mb-4 leading-relaxed">
                    {step.description}
                  </p>
                  
                  {/* Step Details */}
                  <div className="space-y-2">
                    {step.details.map((detail, detailIndex) => (
                      <div 
                        key={detailIndex}
                        className={cn(
                          "flex items-center gap-2 text-sm transition-all duration-300",
                          activeStep === index ? "text-primary" : "text-foreground/60"
                        )}
                      >
                        <div className={cn(
                          "w-1.5 h-1.5 rounded-full transition-colors duration-300",
                          activeStep === index ? "bg-primary" : "bg-foreground/40"
                        )} />
                        <span>{detail}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Interactive Demo Preview */}
        <div className={cn(
          "mt-16 bg-gradient-to-r from-surface/50 to-surface-light/50 backdrop-blur-sm rounded-2xl p-8 border border-primary/20 transition-all duration-1000 delay-1000",
          isVisible ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
        )}>
          <h4 className="text-2xl font-bold text-center mb-8 gradient-text">
            See Step {activeStep + 1} in Action
          </h4>
          
          <div className="max-w-2xl mx-auto">
            {activeStep === 0 && (
              <div className="bg-background/50 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="text-4xl mb-2">📝</div>
                  <h5 className="font-semibold">Creating Your Request</h5>
                </div>
                <div className="space-y-3 text-left">
                  <div className="bg-surface/50 rounded p-3">
                    <div className="text-sm text-foreground/60">Product:</div>
                    <div className="font-medium">MacBook Pro 14-inch</div>
                  </div>
                  <div className="bg-surface/50 rounded p-3">
                    <div className="text-sm text-foreground/60">Budget:</div>
                    <div className="font-medium">$1,800 - $2,200</div>
                  </div>
                  <div className="bg-surface/50 rounded p-3">
                    <div className="text-sm text-foreground/60">Location:</div>
                    <div className="font-medium">San Francisco, CA</div>
                  </div>
                </div>
              </div>
            )}

            {activeStep === 1 && (
              <div className="bg-background/50 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="text-4xl mb-2">🏆</div>
                  <h5 className="font-semibold">Sellers Responding</h5>
                </div>
                <div className="space-y-2">
                  {[
                    { name: "Apple Store SF", price: "$1,999", status: "New offer" },
                    { name: "Best Buy", price: "$2,049", status: "Negotiating" },
                    { name: "Local Tech Shop", price: "$1,899", status: "Best price!" }
                  ].map((offer, index) => (
                    <div key={index} className="bg-surface/50 rounded p-3 flex justify-between items-center animate-pulse-glow" style={{ animationDelay: `${index * 0.5}s` }}>
                      <span>{offer.name}</span>
                      <div className="text-right">
                        <div className="font-bold text-success">{offer.price}</div>
                        <div className="text-xs text-primary">{offer.status}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {activeStep === 2 && (
              <div className="bg-background/50 rounded-lg p-6">
                <div className="text-center mb-4">
                  <div className="text-4xl mb-2">💰</div>
                  <h5 className="font-semibold">Deal Completed</h5>
                </div>
                <div className="bg-success/20 rounded-lg p-4 border border-success/30">
                  <div className="text-center">
                    <div className="text-success font-bold text-lg">✅ Deal Accepted!</div>
                    <div className="text-sm text-foreground/70 mt-2">
                      MacBook Pro 14-inch from Local Tech Shop
                    </div>
                    <div className="text-lg font-bold text-success mt-2">$1,899</div>
                    <div className="text-sm text-success mt-1">
                      💰 Saved $150 compared to retail
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}
