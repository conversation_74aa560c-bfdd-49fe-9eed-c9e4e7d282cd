// BestzDealAi - Sample Data for MVP Demo

export const heroData = {
  title: "BestzDealAi",
  subtitle: "AI-Powered Reverse Marketplace",
  description: "Post what you want, sellers compete to offer the best deal. One post — many offers.",
  tagline: "You post it. They deal it.",
  cta: {
    primary: "Start Getting Deals",
    secondary: "See How It Works"
  }
};

export const problemSolutionData = {
  problems: [
    {
      id: 1,
      title: "Time-Consuming Price Hunting",
      description: "Spending hours comparing prices across multiple stores and websites",
      icon: "⏰",
      stat: "45 min average per purchase"
    },
    {
      id: 2,
      title: "Limited Local Discovery",
      description: "Missing out on great deals from local sellers you never knew existed",
      icon: "📍",
      stat: "79% struggle to find local sellers"
    },
    {
      id: 3,
      title: "No Direct Negotiation",
      description: "Unable to negotiate or get personalized offers from sellers",
      icon: "💬",
      stat: "82% want to negotiate prices"
    }
  ],
  solutions: [
    {
      id: 1,
      title: "Sellers Come to You",
      description: "Post once and receive multiple competitive offers automatically",
      icon: "🎯",
      benefit: "Save 90% of search time"
    },
    {
      id: 2,
      title: "AI-Powered Matching",
      description: "Smart algorithm finds the best value deals, not just lowest prices",
      icon: "🤖",
      benefit: "Get 30% better deals"
    },
    {
      id: 3,
      title: "Real-Time Competition",
      description: "Watch sellers compete live with instant notifications and updates",
      icon: "⚡",
      benefit: "Prices drop in real-time"
    }
  ]
};

export const processSteps = [
  {
    id: 1,
    title: "Post Your Request",
    description: "Tell us what you want, your budget, and preferences in 30 seconds",
    icon: "📝",
    details: ["Product description", "Budget range", "Location preference", "Timeline"]
  },
  {
    id: 2,
    title: "Sellers Compete",
    description: "Local and online sellers submit their best offers with real photos",
    icon: "🏆",
    details: ["Multiple offers", "Real product photos", "Seller ratings", "Delivery options"]
  },
  {
    id: 3,
    title: "Choose & Save",
    description: "Compare offers, chat with sellers, and pick the perfect deal",
    icon: "💰",
    details: ["Smart comparison", "Direct messaging", "Secure payment", "Deal completion"]
  }
];

export const featuresData = [
  {
    id: 1,
    title: "Smart Deal Ranking",
    description: "AI analyzes price, quality, seller rating, and delivery time to rank offers",
    icon: "🧠",
    category: "AI Technology",
    demo: "ranking"
  },
  {
    id: 2,
    title: "Real-Time Offers",
    description: "Watch offers come in live with instant notifications and price updates",
    icon: "📱",
    category: "Live Updates",
    demo: "realtime"
  },
  {
    id: 3,
    title: "Local & Online Sellers",
    description: "Connect with both neighborhood shops and online retailers in one place",
    icon: "🌐",
    category: "Marketplace",
    demo: "sellers"
  },
  {
    id: 4,
    title: "Secure Messaging",
    description: "Built-in chat system with offer context and file sharing capabilities",
    icon: "💬",
    category: "Communication",
    demo: "chat"
  },
  {
    id: 5,
    title: "Deal Analytics",
    description: "Track your savings, compare market prices, and optimize future requests",
    icon: "📊",
    category: "Analytics",
    demo: "analytics"
  },
  {
    id: 6,
    title: "Mobile-First Design",
    description: "Seamless experience across all devices with offline capability",
    icon: "📲",
    category: "Experience",
    demo: "mobile"
  }
];

export const competitorData = [
  {
    feature: "Buyer-Initiated Requests",
    bestzdealai: true,
    amazon: false,
    ebay: false,
    facebook: false
  },
  {
    feature: "AI Deal Ranking",
    bestzdealai: true,
    amazon: false,
    ebay: false,
    facebook: false
  },
  {
    feature: "Real-Time Competition",
    bestzdealai: true,
    amazon: false,
    ebay: true,
    facebook: false
  },
  {
    feature: "Local Seller Discovery",
    bestzdealai: true,
    amazon: false,
    ebay: false,
    facebook: true
  },
  {
    feature: "Free for Buyers",
    bestzdealai: true,
    amazon: true,
    ebay: true,
    facebook: true
  },
  {
    feature: "Integrated Chat",
    bestzdealai: true,
    amazon: false,
    ebay: true,
    facebook: true
  }
];

export const testimonialsData = [
  {
    id: 1,
    name: "Sarah Chen",
    role: "Small Business Owner",
    avatar: "👩‍💼",
    rating: 5,
    text: "BestzDealAi helped me find customers I never would have reached. My sales increased 40% in the first month!",
    location: "San Francisco, CA"
  },
  {
    id: 2,
    name: "Mike Rodriguez",
    role: "Deal Hunter",
    avatar: "👨‍💻",
    rating: 5,
    text: "I saved $800 on electronics last month. Sellers actually compete for my business - it's amazing!",
    location: "Austin, TX"
  },
  {
    id: 3,
    name: "Emma Thompson",
    role: "Busy Parent",
    avatar: "👩‍👧",
    rating: 5,
    text: "No more endless price comparisons. I post what I need and get the best deals delivered to my inbox.",
    location: "Seattle, WA"
  },
  {
    id: 4,
    name: "David Kim",
    role: "Local Shop Owner",
    avatar: "👨‍🔧",
    rating: 5,
    text: "Finally, a platform where I can compete with big retailers. My local expertise actually matters here.",
    location: "Portland, OR"
  }
];

export const pricingData = [
  {
    id: "buyer",
    name: "For Buyers",
    price: "Free",
    period: "Forever",
    description: "Get the best deals without any fees",
    features: [
      "Unlimited deal requests",
      "AI-powered offer ranking",
      "Real-time notifications",
      "Secure messaging",
      "Deal analytics",
      "Mobile app access"
    ],
    cta: "Start Getting Deals",
    popular: false,
    color: "primary"
  },
  {
    id: "seller-basic",
    name: "Seller Basic",
    price: "$0",
    period: "/month",
    description: "Perfect for occasional sellers",
    features: [
      "5 offers per month",
      "Basic seller profile",
      "Standard support",
      "Deal completion tracking",
      "Basic analytics"
    ],
    cta: "Start Selling",
    popular: false,
    color: "surface"
  },
  {
    id: "seller-pro",
    name: "Seller Pro",
    price: "$19.99",
    period: "/month",
    description: "For active sellers and small businesses",
    features: [
      "Unlimited offers",
      "Priority placement",
      "Advanced analytics",
      "Custom seller profile",
      "Priority support",
      "Bulk offer tools",
      "API access"
    ],
    cta: "Go Pro",
    popular: true,
    color: "accent"
  }
];

export const trustData = [
  {
    id: 1,
    title: "Secure Payments",
    description: "Bank-level encryption and secure payment processing",
    icon: "🔒"
  },
  {
    id: 2,
    title: "Verified Sellers",
    description: "All sellers go through identity and business verification",
    icon: "✅"
  },
  {
    id: 3,
    title: "Money-Back Guarantee",
    description: "100% satisfaction guarantee or your money back",
    icon: "💯"
  },
  {
    id: 4,
    title: "24/7 Support",
    description: "Round-the-clock customer support and dispute resolution",
    icon: "🛟"
  }
];

export const demoLevels = [
  {
    id: "basic",
    name: "Basic Demo",
    description: "Simple product request with 2-3 offers",
    request: {
      product: "iPhone 15 Pro",
      budget: "$800-900",
      location: "San Francisco, CA",
      timeline: "This week"
    },
    offers: [
      {
        id: 1,
        seller: "TechHub SF",
        price: 849,
        condition: "New",
        rating: 4.8,
        delivery: "2 days",
        image: "📱"
      },
      {
        id: 2,
        seller: "Mobile World",
        price: 829,
        condition: "New",
        rating: 4.6,
        delivery: "3 days",
        image: "📱"
      }
    ]
  },
  {
    id: "good",
    name: "Enhanced Demo",
    description: "Multiple offers with negotiation simulation",
    request: {
      product: "MacBook Pro 14-inch",
      budget: "$1800-2200",
      location: "Austin, TX",
      timeline: "Next week"
    },
    offers: [
      {
        id: 1,
        seller: "Austin Electronics",
        price: 1999,
        condition: "New",
        rating: 4.9,
        delivery: "1 day",
        image: "💻",
        negotiable: true
      },
      {
        id: 2,
        seller: "Tech Deals Pro",
        price: 1899,
        condition: "Open Box",
        rating: 4.7,
        delivery: "2 days",
        image: "💻",
        negotiable: true
      },
      {
        id: 3,
        seller: "Local Computer Store",
        price: 2050,
        condition: "New",
        rating: 4.8,
        delivery: "Same day",
        image: "💻",
        negotiable: false
      }
    ]
  },
  {
    id: "best",
    name: "Full Experience",
    description: "Complete marketplace with chat, images, and ratings",
    request: {
      product: "Gaming Setup (PC + Monitor + Accessories)",
      budget: "$2000-3000",
      location: "Seattle, WA",
      timeline: "Flexible"
    },
    offers: [
      {
        id: 1,
        seller: "Gaming Paradise",
        price: 2499,
        condition: "New",
        rating: 4.9,
        delivery: "3 days",
        image: "🖥️",
        negotiable: true,
        chat: true,
        verified: true
      },
      {
        id: 2,
        seller: "PC Builder Pro",
        price: 2299,
        condition: "Custom Built",
        rating: 4.8,
        delivery: "5 days",
        image: "🖥️",
        negotiable: true,
        chat: true,
        verified: true
      },
      {
        id: 3,
        seller: "Tech Warehouse",
        price: 2799,
        condition: "New",
        rating: 4.6,
        delivery: "2 days",
        image: "🖥️",
        negotiable: false,
        chat: true,
        verified: false
      }
    ]
  }
];
